================================================================================
ROV移动控制日志
开始时间: 2025-07-31 17:21:12
Unity版本: 2022.3.55f1c1
场景名称: ROV
================================================================================

=== ROV控制状态 Frame 40 Time 0.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.533, 2.742, 0.015)
位置误差向量: (10.002, 6.785, 3.325), 距离: 12.5352m
当前速度: (0.1318, 0.0746, 0.2420), 速度大小: 0.2854m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-100.276, Y:-67.999, Z:-33.731
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 50 Time 1.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.578, 2.768, 0.093)
位置误差向量: (9.956, 6.759, 3.247), 距离: 12.4636m
当前速度: (0.2879, 0.1716, 0.4828), 速度大小: 0.5878m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-100.121, Y:-67.921, Z:-33.438
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 60 Time 1.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.643, 2.808, 0.205)
位置误差向量: (9.892, 6.719, 3.135), 距离: 12.3620m
当前速度: (0.3399, 0.2153, 0.5953), 速度大小: 0.7185m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-99.579, Y:-67.609, Z:-32.567
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 70 Time 1.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.713, 2.853, 0.326)
位置误差向量: (9.822, 6.674, 3.014), 距离: 12.2508m
当前速度: (0.3571, 0.2320, 0.6161), 速度大小: 0.7489m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-98.911, Y:-67.190, Z:-31.406
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 80 Time 1.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.785, 2.900, 0.451)
位置误差向量: (9.749, 6.627, 2.889), 距离: 12.1371m
当前速度: (0.3647, 0.2356, 0.6240), 速度大小: 0.7602m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-98.203, Y:-66.728, Z:-30.198
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 90 Time 1.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.859, 2.947, 0.576)
位置误差向量: (9.676, 6.580, 2.764), 距离: 12.0232m
当前速度: (0.3672, 0.2344, 0.6257), 速度大小: 0.7625m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-97.474, Y:-66.256, Z:-28.968
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 100 Time 2.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (19.932, 2.994, 0.701)
位置误差向量: (9.602, 6.533, 2.639), 距离: 11.9102m
当前速度: (0.3670, 0.2319, 0.6269), 速度大小: 0.7626m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-96.739, Y:-65.784, Z:-27.733
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 110 Time 2.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.005, 3.040, 0.826)
位置误差向量: (9.529, 6.487, 2.514), 距离: 11.7986m
当前速度: (0.3636, 0.2288, 0.6284), 速度大小: 0.7612m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-96.002, Y:-65.318, Z:-26.496
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 120 Time 2.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.077, 3.085, 0.952)
位置误差向量: (9.457, 6.442, 2.388), 距离: 11.6895m
当前速度: (0.3572, 0.2201, 0.6302), 速度大小: 0.7570m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-95.269, Y:-64.852, Z:-25.256
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 130 Time 2.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.149, 3.121, 1.083)
位置误差向量: (9.385, 6.406, 2.257), 距离: 11.5850m
当前速度: (0.4326, 0.1525, 0.7556), 速度大小: 0.8839m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-94.694, Y:-64.358, Z:-24.211
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 140 Time 2.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.229, 3.156, 1.217)
位置误差向量: (9.306, 6.371, 2.123), 距离: 11.4754m
当前速度: (0.3819, 0.1953, 0.6434), 速度大小: 0.7733m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-93.801, Y:-64.087, Z:-22.659
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 150 Time 3.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.303, 3.198, 1.346)
位置误差向量: (9.231, 6.328, 1.994), 距离: 11.3682m
当前速度: (0.3660, 0.2206, 0.6463), 速度大小: 0.7748m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-93.024, Y:-63.715, Z:-21.385
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 160 Time 3.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.376, 3.244, 1.475)
位置误差向量: (9.159, 6.283, 1.865), 距离: 11.2623m
当前速度: (0.3579, 0.2332, 0.6370), 速度大小: 0.7670m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-92.287, Y:-63.283, Z:-20.098
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 170 Time 3.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.447, 3.292, 1.602)
位置误差向量: (9.088, 6.235, 1.738), 距离: 11.1575m
当前速度: (0.3532, 0.2398, 0.6388), 速度大小: 0.7684m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-91.567, Y:-62.822, Z:-18.837
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 180 Time 3.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.517, 3.340, 1.730)
位置误差向量: (9.018, 6.187, 1.610), 距离: 11.0540m
当前速度: (0.3494, 0.2428, 0.6392), 速度大小: 0.7679m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-90.858, Y:-62.344, Z:-17.569
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 190 Time 3.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.586, 3.389, 1.858)
位置误差向量: (8.948, 6.138, 1.482), 距离: 10.9519m
当前速度: (0.3464, 0.2442, 0.6404), 速度大小: 0.7680m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-90.156, Y:-61.859, Z:-16.301
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-1.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 200 Time 4.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.655, 3.438, 1.985)
位置误差向量: (8.879, 6.089, 1.355), 距离: 10.8514m
当前速度: (0.3450, 0.2450, 0.6276), 速度大小: 0.7570m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-89.463, Y:-61.372, Z:-15.011
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.888, RotY=0.000
==================================================

=== ROV控制状态 Frame 210 Time 4.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.725, 3.487, 2.106)
位置误差向量: (8.810, 6.040, 1.234), 距离: 10.7525m
当前速度: (0.3497, 0.2476, 0.5820), 速度大小: 0.7227m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-88.778, Y:-60.884, Z:-13.721
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.733, RotY=0.000
==================================================

=== ROV控制状态 Frame 220 Time 4.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.796, 3.537, 2.216)
位置误差向量: (8.739, 5.990, 1.124), 距离: 10.6542m
当前速度: (0.3586, 0.2522, 0.5225), 速度大小: 0.6820m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-88.087, Y:-60.393, Z:-12.512
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.583, RotY=0.000
==================================================

=== ROV控制状态 Frame 230 Time 4.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.869, 3.588, 2.314)
位置误差向量: (8.666, 5.939, 1.026), 距离: 10.5557m
当前速度: (0.3702, 0.2582, 0.4581), 速度大小: 0.6431m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-87.380, Y:-59.894, Z:-11.418
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.444, RotY=0.000
==================================================

=== ROV控制状态 Frame 240 Time 4.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (20.944, 3.640, 2.398)
位置误差向量: (8.590, 5.887, 0.942), 距离: 10.4563m
当前速度: (0.3833, 0.2649, 0.3915), 速度大小: 0.6086m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-86.651, Y:-59.384, Z:-10.451
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.318, RotY=0.000
==================================================

=== ROV控制状态 Frame 250 Time 5.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.022, 3.694, 2.469)
位置误差向量: (8.512, 5.833, 0.871), 距离: 10.3557m
当前速度: (0.3969, 0.2719, 0.3236), 速度大小: 0.5798m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-85.896, Y:-58.860, Z:-9.616
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.205, RotY=0.000
==================================================

=== ROV控制状态 Frame 260 Time 5.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.103, 3.749, 2.526)
位置误差向量: (8.431, 5.778, 0.814), 距离: 10.2534m
当前速度: (0.4106, 0.2788, 0.2553), 速度大小: 0.5581m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-85.114, Y:-58.322, Z:-8.916
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.106, RotY=0.000
==================================================

=== ROV控制状态 Frame 270 Time 5.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.187, 3.806, 2.570)
位置误差向量: (8.348, 5.721, 0.770), 距离: 10.1495m
当前速度: (0.4234, 0.2849, 0.1876), 速度大小: 0.5438m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-84.303, Y:-57.769, Z:-8.352
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=-0.021, RotY=0.000
==================================================

=== ROV控制状态 Frame 280 Time 5.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.273, 3.863, 2.600)
位置误差向量: (8.262, 5.664, 0.740), 距离: 10.0441m
当前速度: (0.4349, 0.2899, 0.1222), 速度大小: 0.5367m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-83.466, Y:-57.204, Z:-7.927
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.050, RotY=0.000
==================================================

=== ROV控制状态 Frame 290 Time 5.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.361, 3.922, 2.617)
位置误差向量: (8.174, 5.605, 0.723), 距离: 9.9374m
当前速度: (0.4445, 0.2932, 0.0614), 速度大小: 0.5360m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-82.604, Y:-56.626, Z:-7.636
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.106, RotY=0.000
==================================================

=== ROV控制状态 Frame 300 Time 6.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.451, 3.980, 2.624)
位置误差向量: (8.084, 5.547, 0.716), 距离: 9.8300m
当前速度: (0.4519, 0.2947, 0.0080), 速度大小: 0.5395m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-81.721, Y:-56.041, Z:-7.473
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.149, RotY=0.000
==================================================

=== ROV控制状态 Frame 310 Time 6.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.541, 4.039, 2.620)
位置误差向量: (7.993, 5.488, 0.720), 距离: 9.7222m
当前速度: (0.4570, 0.2946, -0.0360), 速度大小: 0.5449m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-80.821, Y:-55.451, Z:-7.426
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.179, RotY=0.000
==================================================

=== ROV控制状态 Frame 320 Time 6.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.633, 4.098, 2.609)
位置误差向量: (7.901, 5.429, 0.731), 距离: 9.6143m
当前速度: (0.4602, 0.2935, -0.0695), 速度大小: 0.5503m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-79.910, Y:-54.861, Z:-7.476
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.198, RotY=0.000
==================================================

=== ROV控制状态 Frame 330 Time 6.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.726, 4.157, 2.593)
位置误差向量: (7.809, 5.370, 0.747), 距离: 9.5067m
当前速度: (0.4622, 0.2920, -0.0931), 速度大小: 0.5546m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-78.991, Y:-54.273, Z:-7.601
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.208, RotY=0.000
==================================================

=== ROV控制状态 Frame 340 Time 6.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.818, 4.215, 2.572)
位置误差向量: (7.716, 5.312, 0.768), 距离: 9.3995m
当前速度: (0.4633, 0.2905, -0.1079), 速度大小: 0.5574m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-78.067, Y:-53.687, Z:-7.780
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.213, RotY=0.000
==================================================

=== ROV控制状态 Frame 350 Time 7.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (21.911, 4.273, 2.550)
位置误差向量: (7.624, 5.254, 0.790), 距离: 9.2925m
当前速度: (0.4640, 0.2892, -0.1160), 速度大小: 0.5589m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-77.141, Y:-53.105, Z:-7.995
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.212, RotY=0.000
==================================================

=== ROV控制状态 Frame 360 Time 7.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.004, 4.331, 2.526)
位置误差向量: (7.531, 5.196, 0.814), 距离: 9.1857m
当前速度: (0.4643, 0.2884, -0.1189), 速度大小: 0.5594m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-76.213, Y:-52.526, Z:-8.230
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.210, RotY=0.000
==================================================

=== ROV控制状态 Frame 370 Time 7.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.097, 4.388, 2.502)
位置误差向量: (7.438, 5.139, 0.838), 距离: 9.0791m
当前速度: (0.4646, 0.2878, -0.1183), 速度大小: 0.5592m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-75.285, Y:-51.949, Z:-8.474
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.205, RotY=0.000
==================================================

=== ROV控制状态 Frame 380 Time 7.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.190, 4.446, 2.479)
位置误差向量: (7.345, 5.081, 0.861), 距离: 8.9726m
当前速度: (0.4648, 0.2876, -0.1153), 速度大小: 0.5586m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-74.355, Y:-51.373, Z:-8.718
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.200, RotY=0.000
==================================================

=== ROV控制状态 Frame 390 Time 7.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.283, 4.503, 2.456)
位置误差向量: (7.252, 5.024, 0.884), 距离: 8.8662m
当前速度: (0.4650, 0.2875, -0.1108), 速度大小: 0.5578m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-73.426, Y:-50.798, Z:-8.958
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.195, RotY=0.000
==================================================

=== ROV控制状态 Frame 400 Time 8.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.376, 4.561, 2.435)
位置误差向量: (7.159, 4.966, 0.905), 距离: 8.7597m
当前速度: (0.4652, 0.2876, -0.1055), 速度大小: 0.5570m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-72.496, Y:-50.241, Z:-9.189
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.189, RotY=0.000
==================================================

=== ROV控制状态 Frame 410 Time 8.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.469, 4.618, 2.414)
位置误差向量: (7.066, 4.909, 0.926), 距离: 8.6532m
当前速度: (0.4652, 0.2878, -0.0999), 速度大小: 0.5561m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-71.566, Y:-49.695, Z:-9.411
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.184, RotY=0.000
==================================================

=== ROV控制状态 Frame 420 Time 8.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.562, 4.676, 2.395)
位置误差向量: (6.973, 4.851, 0.945), 距离: 8.5467m
当前速度: (0.4654, 0.2880, -0.0941), 速度大小: 0.5554m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-70.635, Y:-49.149, Z:-9.621
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.180, RotY=0.000
==================================================

=== ROV控制状态 Frame 430 Time 8.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.655, 4.734, 2.377)
位置误差向量: (6.880, 4.793, 0.963), 距离: 8.4401m
当前速度: (0.4655, 0.2883, -0.0885), 速度大小: 0.5546m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-69.705, Y:-48.602, Z:-9.820
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.176, RotY=0.000
==================================================

=== ROV控制状态 Frame 440 Time 8.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.748, 4.791, 2.360)
位置误差向量: (6.787, 4.736, 0.980), 距离: 8.3334m
当前速度: (0.4656, 0.2885, -0.0830), 速度大小: 0.5540m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-68.774, Y:-48.055, Z:-10.007
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.172, RotY=0.000
==================================================

=== ROV控制状态 Frame 450 Time 9.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.841, 4.849, 2.344)
位置误差向量: (6.693, 4.678, 0.996), 距离: 8.2267m
当前速度: (0.4657, 0.2887, -0.0778), 速度大小: 0.5534m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-67.843, Y:-47.506, Z:-10.184
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.168, RotY=0.000
==================================================

=== ROV控制状态 Frame 460 Time 9.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (22.934, 4.907, 2.329)
位置误差向量: (6.600, 4.620, 1.011), 距离: 8.1200m
当前速度: (0.4657, 0.2889, -0.0728), 速度大小: 0.5529m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-66.911, Y:-46.957, Z:-10.349
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.165, RotY=0.000
==================================================

=== ROV控制状态 Frame 470 Time 9.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.027, 4.965, 2.315)
位置误差向量: (6.507, 4.562, 1.025), 距离: 8.0132m
当前速度: (0.4658, 0.2891, -0.0680), 速度大小: 0.5524m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-65.980, Y:-46.406, Z:-10.505
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.163, RotY=0.000
==================================================

=== ROV控制状态 Frame 480 Time 9.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.120, 5.022, 2.301)
位置误差向量: (6.414, 4.505, 1.039), 距离: 7.9063m
当前速度: (0.4658, 0.2893, -0.0635), 速度大小: 0.5520m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-65.048, Y:-45.855, Z:-10.651
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.160, RotY=0.000
==================================================

=== ROV控制状态 Frame 490 Time 9.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.214, 5.080, 2.289)
位置误差向量: (6.321, 4.447, 1.051), 距离: 7.7994m
当前速度: (0.4658, 0.2894, -0.0591), 速度大小: 0.5516m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-64.117, Y:-45.304, Z:-10.788
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.157, RotY=0.000
==================================================

=== ROV控制状态 Frame 500 Time 10.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.307, 5.138, 2.278)
位置误差向量: (6.228, 4.389, 1.062), 距离: 7.6925m
当前速度: (0.4658, 0.2896, -0.0549), 速度大小: 0.5512m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-63.185, Y:-44.752, Z:-10.916
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.155, RotY=0.000
==================================================

=== ROV控制状态 Frame 510 Time 10.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.400, 5.196, 2.267)
位置误差向量: (6.134, 4.331, 1.073), 距离: 7.5855m
当前速度: (0.4659, 0.2897, -0.0508), 速度大小: 0.5509m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-62.253, Y:-44.199, Z:-11.036
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.153, RotY=0.000
==================================================

=== ROV控制状态 Frame 520 Time 10.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.493, 5.254, 2.258)
位置误差向量: (6.041, 4.273, 1.082), 距离: 7.4785m
当前速度: (0.4658, 0.2898, -0.0468), 速度大小: 0.5505m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-61.322, Y:-43.645, Z:-11.147
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.150, RotY=0.000
==================================================

=== ROV控制状态 Frame 530 Time 10.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.586, 5.312, 2.249)
位置误差向量: (5.948, 4.215, 1.091), 距离: 7.3714m
当前速度: (0.4658, 0.2899, -0.0428), 速度大小: 0.5503m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-60.390, Y:-43.091, Z:-11.251
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.148, RotY=0.000
==================================================

=== ROV控制状态 Frame 540 Time 10.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.679, 5.370, 2.241)
位置误差向量: (5.855, 4.157, 1.099), 距离: 7.2643m
当前速度: (0.4657, 0.2900, -0.0389), 速度大小: 0.5500m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-59.458, Y:-42.536, Z:-11.346
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.146, RotY=0.000
==================================================

=== ROV控制状态 Frame 550 Time 11.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.773, 5.428, 2.233)
位置误差向量: (5.762, 4.099, 1.107), 距离: 7.1572m
当前速度: (0.4657, 0.2901, -0.0350), 速度大小: 0.5498m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-58.527, Y:-41.981, Z:-11.434
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.144, RotY=0.000
==================================================

=== ROV控制状态 Frame 560 Time 11.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.866, 5.486, 2.227)
位置误差向量: (5.669, 4.041, 1.113), 距离: 7.0501m
当前速度: (0.4655, 0.2902, -0.0311), 速度大小: 0.5494m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-57.596, Y:-41.426, Z:-11.514
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.142, RotY=0.000
==================================================

=== ROV控制状态 Frame 570 Time 11.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (23.959, 5.544, 2.221)
位置误差向量: (5.576, 3.983, 1.119), 距离: 6.9430m
当前速度: (0.4654, 0.2903, -0.0272), 速度大小: 0.5492m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-56.665, Y:-40.869, Z:-11.586
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.139, RotY=0.000
==================================================

=== ROV控制状态 Frame 580 Time 11.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.052, 5.602, 2.216)
位置误差向量: (5.483, 3.925, 1.124), 距离: 6.8358m
当前速度: (0.4651, 0.2903, -0.0234), 速度大小: 0.5488m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-55.734, Y:-40.313, Z:-11.650
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.137, RotY=0.000
==================================================

=== ROV控制状态 Frame 590 Time 11.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.145, 5.660, 2.212)
位置误差向量: (5.390, 3.867, 1.128), 距离: 6.7286m
当前速度: (0.4649, 0.2904, -0.0195), 速度大小: 0.5485m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-54.803, Y:-39.755, Z:-11.707
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.135, RotY=0.000
==================================================

=== ROV控制状态 Frame 600 Time 12.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.238, 5.718, 2.208)
位置误差向量: (5.297, 3.809, 1.132), 距离: 6.6213m
当前速度: (0.4647, 0.2905, -0.0156), 速度大小: 0.5483m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-53.873, Y:-39.198, Z:-11.756
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.133, RotY=0.000
==================================================

=== ROV控制状态 Frame 610 Time 12.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.331, 5.776, 2.205)
位置误差向量: (5.204, 3.751, 1.135), 距离: 6.5141m
当前速度: (0.4644, 0.2906, -0.0117), 速度大小: 0.5480m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-52.943, Y:-38.640, Z:-11.798
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.131, RotY=0.000
==================================================

=== ROV控制状态 Frame 620 Time 12.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.424, 5.834, 2.204)
位置误差向量: (5.111, 3.692, 1.136), 距离: 6.4068m
当前速度: (0.4642, 0.2907, -0.0079), 速度大小: 0.5477m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-52.014, Y:-38.081, Z:-11.831
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.129, RotY=0.000
==================================================

=== ROV控制状态 Frame 630 Time 12.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.516, 5.893, 2.202)
位置误差向量: (5.018, 3.634, 1.138), 距离: 6.2995m
当前速度: (0.4639, 0.2907, -0.0040), 速度大小: 0.5475m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-51.086, Y:-37.521, Z:-11.857
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.126, RotY=0.000
==================================================

=== ROV控制状态 Frame 640 Time 12.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.609, 5.951, 2.202)
位置误差向量: (4.925, 3.576, 1.138), 距离: 6.1922m
当前速度: (0.4636, 0.2908, -0.0002), 速度大小: 0.5472m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-50.185, Y:-36.962, Z:-11.875
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.125, RotY=0.000
==================================================

=== ROV控制状态 Frame 650 Time 13.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.702, 6.009, 2.202)
位置误差向量: (4.833, 3.518, 1.138), 距离: 6.0849m
当前速度: (0.4633, 0.2909, 0.0034), 速度大小: 0.5471m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-49.286, Y:-36.401, Z:-11.885
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.123, RotY=0.000
==================================================

=== ROV控制状态 Frame 660 Time 13.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.794, 6.067, 2.203)
位置误差向量: (4.740, 3.460, 1.137), 距离: 5.9775m
当前速度: (0.4630, 0.2909, 0.0068), 速度大小: 0.5469m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-48.388, Y:-35.841, Z:-11.888
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.122, RotY=0.000
==================================================

=== ROV控制状态 Frame 670 Time 13.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.887, 6.125, 2.205)
位置误差向量: (4.648, 3.402, 1.135), 距离: 5.8701m
当前速度: (0.4626, 0.2910, 0.0102), 速度大小: 0.5466m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-47.490, Y:-35.279, Z:-11.884
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.120, RotY=0.000
==================================================

=== ROV控制状态 Frame 680 Time 13.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (24.979, 6.184, 2.208)
位置误差向量: (4.555, 3.343, 1.132), 距离: 5.7627m
当前速度: (0.4623, 0.2911, 0.0136), 速度大小: 0.5465m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-46.592, Y:-34.718, Z:-11.873
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.119, RotY=0.000
==================================================

=== ROV控制状态 Frame 690 Time 13.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.072, 6.242, 2.211)
位置误差向量: (4.463, 3.285, 1.129), 距离: 5.6553m
当前速度: (0.4620, 0.2911, 0.0171), 速度大小: 0.5463m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-45.694, Y:-34.155, Z:-11.856
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.117, RotY=0.000
==================================================

=== ROV控制状态 Frame 700 Time 14.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.164, 6.300, 2.215)
位置误差向量: (4.370, 3.227, 1.125), 距离: 5.5479m
当前速度: (0.4616, 0.2912, 0.0206), 速度大小: 0.5461m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-44.796, Y:-33.593, Z:-11.832
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.115, RotY=0.000
==================================================

=== ROV控制状态 Frame 710 Time 14.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.256, 6.358, 2.219)
位置误差向量: (4.278, 3.169, 1.121), 距离: 5.4405m
当前速度: (0.4611, 0.2912, 0.0241), 速度大小: 0.5459m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-43.899, Y:-33.030, Z:-11.800
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.113, RotY=0.000
==================================================

=== ROV控制状态 Frame 720 Time 14.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.349, 6.416, 2.224)
位置误差向量: (4.186, 3.110, 1.116), 距离: 5.3330m
当前速度: (0.4606, 0.2913, 0.0278), 速度大小: 0.5457m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-43.001, Y:-32.466, Z:-11.762
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.111, RotY=0.000
==================================================

=== ROV控制状态 Frame 730 Time 14.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.441, 6.475, 2.230)
位置误差向量: (4.094, 3.052, 1.110), 距离: 5.2256m
当前速度: (0.4601, 0.2913, 0.0314), 速度大小: 0.5454m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-42.104, Y:-31.902, Z:-11.716
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.109, RotY=0.000
==================================================

=== ROV控制状态 Frame 740 Time 14.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.533, 6.533, 2.237)
位置误差向量: (4.002, 2.994, 1.103), 距离: 5.1181m
当前速度: (0.4595, 0.2914, 0.0352), 速度大小: 0.5452m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-41.208, Y:-31.337, Z:-11.663
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.107, RotY=0.000
==================================================

=== ROV控制状态 Frame 750 Time 15.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.625, 6.591, 2.244)
位置误差向量: (3.910, 2.936, 1.096), 距离: 5.0106m
当前速度: (0.4589, 0.2915, 0.0390), 速度大小: 0.5450m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-40.312, Y:-30.773, Z:-11.602
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.105, RotY=0.000
==================================================

=== ROV控制状态 Frame 760 Time 15.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.716, 6.650, 2.253)
位置误差向量: (3.818, 2.877, 1.087), 距离: 4.9031m
当前速度: (0.4583, 0.2915, 0.0428), 速度大小: 0.5449m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-39.417, Y:-30.207, Z:-11.534
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.102, RotY=0.000
==================================================

=== ROV控制状态 Frame 770 Time 15.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.808, 6.708, 2.262)
位置误差向量: (3.727, 2.819, 1.078), 距离: 4.7956m
当前速度: (0.4576, 0.2916, 0.0466), 速度大小: 0.5446m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-38.522, Y:-29.641, Z:-11.459
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.100, RotY=0.000
==================================================

=== ROV控制状态 Frame 780 Time 15.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.899, 6.766, 2.271)
位置误差向量: (3.635, 2.761, 1.069), 距离: 4.6881m
当前速度: (0.4569, 0.2917, 0.0504), 速度大小: 0.5444m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-37.629, Y:-29.075, Z:-11.375
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.098, RotY=0.000
==================================================

=== ROV控制状态 Frame 790 Time 15.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (25.991, 6.825, 2.282)
位置误差向量: (3.544, 2.702, 1.058), 距离: 4.5806m
当前速度: (0.4561, 0.2917, 0.0543), 速度大小: 0.5442m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-36.736, Y:-28.508, Z:-11.284
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.096, RotY=0.000
==================================================

=== ROV控制状态 Frame 800 Time 16.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.082, 6.883, 2.293)
位置误差向量: (3.453, 2.644, 1.047), 距离: 4.4731m
当前速度: (0.4554, 0.2918, 0.0582), 速度大小: 0.5440m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-35.844, Y:-27.940, Z:-11.185
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.094, RotY=0.000
==================================================

=== ROV控制状态 Frame 810 Time 16.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.173, 6.941, 2.305)
位置误差向量: (3.362, 2.586, 1.035), 距离: 4.3655m
当前速度: (0.4546, 0.2919, 0.0620), 速度大小: 0.5438m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-34.953, Y:-27.372, Z:-11.078
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.092, RotY=0.000
==================================================

=== ROV控制状态 Frame 820 Time 16.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.264, 7.000, 2.318)
位置误差向量: (3.271, 2.527, 1.022), 距离: 4.2580m
当前速度: (0.4538, 0.2920, 0.0659), 速度大小: 0.5436m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-34.063, Y:-26.804, Z:-10.963
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.089, RotY=0.000
==================================================

=== ROV控制状态 Frame 830 Time 16.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.354, 7.058, 2.332)
位置误差向量: (3.180, 2.469, 1.008), 距离: 4.1504m
当前速度: (0.4529, 0.2921, 0.0698), 速度大小: 0.5434m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-33.174, Y:-26.235, Z:-10.841
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.087, RotY=0.000
==================================================

=== ROV控制状态 Frame 840 Time 16.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.445, 7.117, 2.346)
位置误差向量: (3.090, 2.410, 0.994), 距离: 4.0429m
当前速度: (0.4520, 0.2922, 0.0736), 速度大小: 0.5432m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-32.286, Y:-25.666, Z:-10.711
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.085, RotY=0.000
==================================================

=== ROV控制状态 Frame 850 Time 17.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.535, 7.175, 2.361)
位置误差向量: (2.999, 2.352, 0.979), 距离: 3.9353m
当前速度: (0.4511, 0.2922, 0.0775), 速度大小: 0.5430m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-31.399, Y:-25.096, Z:-10.573
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.083, RotY=0.000
==================================================

=== ROV控制状态 Frame 860 Time 17.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.625, 7.233, 2.377)
位置误差向量: (2.909, 2.294, 0.963), 距离: 3.8278m
当前速度: (0.4502, 0.2923, 0.0814), 速度大小: 0.5429m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.514, Y:-24.525, Z:-10.427
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.081, RotY=0.000
==================================================

=== ROV控制状态 Frame 870 Time 17.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.715, 7.292, 2.394)
位置误差向量: (2.819, 2.235, 0.946), 距离: 3.7202m
当前速度: (0.4492, 0.2924, 0.0852), 速度大小: 0.5427m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.630, Y:-23.954, Z:-10.273
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.078, RotY=0.000
==================================================

=== ROV控制状态 Frame 880 Time 17.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.805, 7.350, 2.411)
位置误差向量: (2.730, 2.177, 0.929), 距离: 3.6127m
当前速度: (0.4481, 0.2924, 0.0891), 速度大小: 0.5424m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.747, Y:-23.383, Z:-10.112
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.076, RotY=0.000
==================================================

=== ROV控制状态 Frame 890 Time 17.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.894, 7.409, 2.429)
位置误差向量: (2.640, 2.118, 0.911), 距离: 3.5051m
当前速度: (0.4471, 0.2925, 0.0929), 速度大小: 0.5423m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.866, Y:-22.811, Z:-9.942
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.074, RotY=0.000
==================================================

=== ROV控制状态 Frame 900 Time 18.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (26.984, 7.467, 2.448)
位置误差向量: (2.551, 2.060, 0.892), 距离: 3.3976m
当前速度: (0.4461, 0.2926, 0.0968), 速度大小: 0.5422m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.986, Y:-22.239, Z:-9.765
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.072, RotY=0.000
==================================================

=== ROV控制状态 Frame 910 Time 18.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.073, 7.526, 2.468)
位置误差向量: (2.462, 2.001, 0.872), 距离: 3.2901m
当前速度: (0.4450, 0.2926, 0.1006), 速度大小: 0.5420m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.108, Y:-21.666, Z:-9.580
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.070, RotY=0.000
==================================================

=== ROV控制状态 Frame 920 Time 18.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.162, 7.584, 2.489)
位置误差向量: (2.373, 1.943, 0.851), 距离: 3.1825m
当前速度: (0.4438, 0.2927, 0.1045), 速度大小: 0.5418m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-25.232, Y:-21.092, Z:-9.387
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.067, RotY=0.000
==================================================

=== ROV控制状态 Frame 930 Time 18.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.250, 7.643, 2.510)
位置误差向量: (2.284, 1.884, 0.830), 距离: 3.0750m
当前速度: (0.4427, 0.2928, 0.1083), 速度大小: 0.5417m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.357, Y:-20.518, Z:-9.186
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.065, RotY=0.000
==================================================

=== ROV控制状态 Frame 940 Time 18.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.339, 7.702, 2.532)
位置误差向量: (2.196, 1.825, 0.808), 距离: 2.9676m
当前速度: (0.4416, 0.2929, 0.1121), 速度大小: 0.5416m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-23.484, Y:-19.944, Z:-8.978
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.063, RotY=0.000
==================================================

=== ROV控制状态 Frame 950 Time 19.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.427, 7.760, 2.555)
位置误差向量: (2.108, 1.767, 0.785), 距离: 2.8601m
当前速度: (0.4404, 0.2929, 0.1160), 速度大小: 0.5415m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-22.613, Y:-19.369, Z:-8.762
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.061, RotY=0.000
==================================================

=== ROV控制状态 Frame 960 Time 19.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.515, 7.819, 2.579)
位置误差向量: (2.020, 1.708, 0.761), 距离: 2.7526m
当前速度: (0.4391, 0.2930, 0.1198), 速度大小: 0.5413m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-21.743, Y:-18.794, Z:-8.538
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.059, RotY=0.000
==================================================

=== ROV控制状态 Frame 970 Time 19.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.602, 7.877, 2.603)
位置误差向量: (1.932, 1.650, 0.737), 距离: 2.6452m
当前速度: (0.4378, 0.2931, 0.1236), 速度大小: 0.5412m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.876, Y:-18.218, Z:-8.306
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.056, RotY=0.000
==================================================

=== ROV控制状态 Frame 980 Time 19.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.690, 7.936, 2.628)
位置误差向量: (1.845, 1.591, 0.712), 距离: 2.5378m
当前速度: (0.4366, 0.2931, 0.1274), 速度大小: 0.5411m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.010, Y:-17.642, Z:-8.066
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.054, RotY=0.000
==================================================

=== ROV控制状态 Frame 990 Time 19.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.777, 7.995, 2.654)
位置误差向量: (1.757, 1.532, 0.686), 距离: 2.4305m
当前速度: (0.4353, 0.2932, 0.1313), 速度大小: 0.5410m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-19.147, Y:-17.065, Z:-7.819
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.052, RotY=0.000
==================================================

=== ROV控制状态 Frame 1000 Time 20.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.864, 8.053, 2.681)
位置误差向量: (1.671, 1.474, 0.659), 距离: 2.3232m
当前速度: (0.4338, 0.2933, 0.1351), 速度大小: 0.5408m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-18.285, Y:-16.487, Z:-7.564
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.050, RotY=0.000
==================================================

=== ROV控制状态 Frame 1010 Time 20.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (27.951, 8.112, 2.708)
位置误差向量: (1.584, 1.415, 0.632), 距离: 2.2160m
当前速度: (0.4324, 0.2934, 0.1389), 速度大小: 0.5407m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.426, Y:-15.910, Z:-7.301
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.048, RotY=0.000
==================================================

=== ROV控制状态 Frame 1020 Time 20.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.037, 8.171, 2.736)
位置误差向量: (1.498, 1.356, 0.604), 距离: 2.1088m
当前速度: (0.4311, 0.2934, 0.1427), 速度大小: 0.5406m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-16.570, Y:-15.331, Z:-7.030
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.045, RotY=0.000
==================================================

=== ROV控制状态 Frame 1030 Time 20.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.123, 8.229, 2.765)
位置误差向量: (1.412, 1.298, 0.575), 距离: 2.0017m
当前速度: (0.4296, 0.2934, 0.1465), 速度大小: 0.5405m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-15.715, Y:-14.752, Z:-6.751
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.043, RotY=0.000
==================================================

=== ROV控制状态 Frame 1040 Time 20.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.209, 8.288, 2.795)
位置误差向量: (1.326, 1.239, 0.545), 距离: 1.8947m
当前速度: (0.4281, 0.2935, 0.1503), 速度大小: 0.5404m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.863, Y:-14.173, Z:-6.465
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.041, RotY=0.000
==================================================

=== ROV控制状态 Frame 1050 Time 21.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.294, 8.347, 2.825)
位置误差向量: (1.240, 1.180, 0.515), 距离: 1.7878m
当前速度: (0.4266, 0.2936, 0.1540), 速度大小: 0.5403m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.013, Y:-13.593, Z:-6.171
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.039, RotY=0.000
==================================================

=== ROV控制状态 Frame 1060 Time 21.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.379, 8.405, 2.857)
位置误差向量: (1.155, 1.122, 0.483), 距离: 1.6811m
当前速度: (0.4251, 0.2937, 0.1578), 速度大小: 0.5402m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-13.165, Y:-13.013, Z:-5.869
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.037, RotY=0.000
==================================================

=== ROV控制状态 Frame 1070 Time 21.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.464, 8.464, 2.889)
位置误差向量: (1.070, 1.063, 0.451), 距离: 1.5745m
当前速度: (0.4235, 0.2937, 0.1616), 速度大小: 0.5401m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-12.321, Y:-12.432, Z:-5.559
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.034, RotY=0.000
==================================================

=== ROV控制状态 Frame 1080 Time 21.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.549, 8.523, 2.921)
位置误差向量: (0.986, 1.004, 0.419), 距离: 1.4681m
当前速度: (0.4218, 0.2938, 0.1653), 速度大小: 0.5400m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-11.478, Y:-11.851, Z:-5.242
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.032, RotY=0.000
==================================================

=== ROV控制状态 Frame 1090 Time 21.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.633, 8.582, 2.955)
位置误差向量: (0.902, 0.945, 0.385), 距离: 1.3620m
当前速度: (0.4203, 0.2938, 0.1691), 速度大小: 0.5400m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.639, Y:-11.270, Z:-4.917
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.030, RotY=0.000
==================================================

=== ROV控制状态 Frame 1100 Time 22.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.717, 8.640, 2.989)
位置误差向量: (0.818, 0.887, 0.351), 距离: 1.2561m
当前速度: (0.4186, 0.2939, 0.1728), 速度大小: 0.5399m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-9.802, Y:-10.688, Z:-4.584
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-1.000, Y=-1.000, Z=0.028, RotY=0.000
==================================================

=== ROV控制状态 Frame 1110 Time 22.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.800, 8.699, 3.024)
位置误差向量: (0.735, 0.828, 0.316), 距离: 1.1510m
当前速度: (0.4116, 0.2944, 0.1739), 速度大小: 0.5351m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.961, Y:-10.105, Z:-4.240
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-0.929, Y=-1.000, Z=0.025, RotY=0.000
==================================================

=== ROV控制状态 Frame 1120 Time 22.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.880, 8.758, 3.058)
位置误差向量: (0.654, 0.769, 0.282), 距离: 1.0479m
当前速度: (0.3943, 0.2964, 0.1695), 速度大小: 0.5216m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.127, Y:-9.523, Z:-3.890
速度因子: 1.000, 自适应速度因子: 1.000
最终轴输出: X=-0.843, Y=-1.000, Z=0.023, RotY=0.000
==================================================

=== ROV控制状态 Frame 1130 Time 22.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (28.957, 8.818, 3.091)
位置误差向量: (0.578, 0.709, 0.249), 距离: 0.9480m
当前速度: (0.3695, 0.2936, 0.1613), 速度大小: 0.4987m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-7.101, Y:-8.665, Z:-3.439
速度因子: 0.948, 自适应速度因子: 0.948
最终轴输出: X=-0.700, Y=-0.863, Z=0.019, RotY=0.000
==================================================

=== ROV控制状态 Frame 1140 Time 22.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.026, 8.874, 3.122)
位置误差向量: (0.508, 0.653, 0.218), 距离: 0.8554m
当前速度: (0.3289, 0.2743, 0.1453), 速度大小: 0.4523m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.005, Y:-7.640, Z:-2.943
速度因子: 0.855, 自适应速度因子: 0.855
最终轴输出: X=-0.536, Y=-0.683, Z=0.014, RotY=0.000
==================================================

=== ROV控制状态 Frame 1150 Time 23.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.087, 8.926, 3.149)
位置误差向量: (0.447, 0.601, 0.191), 距离: 0.7726m
当前速度: (0.2861, 0.2486, 0.1278), 速度大小: 0.4000m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.094, Y:-6.753, Z:-2.527
速度因子: 0.773, 自适应速度因子: 0.773
最终轴输出: X=-0.413, Y=-0.543, Z=0.010, RotY=0.000
==================================================

=== ROV控制状态 Frame 1160 Time 23.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.140, 8.973, 3.173)
位置误差向量: (0.394, 0.554, 0.167), 距离: 0.7000m
当前速度: (0.2467, 0.2224, 0.1116), 速度大小: 0.3504m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.352, Y:-6.003, Z:-2.184
速度因子: 0.700, 自适应速度因子: 0.700
最终轴输出: X=-0.322, Y=-0.435, Z=0.007, RotY=0.000
==================================================

=== ROV控制状态 Frame 1170 Time 23.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.186, 9.015, 3.194)
位置误差向量: (0.349, 0.512, 0.146), 距离: 0.6365m
当前速度: (0.2125, 0.1980, 0.0973), 速度大小: 0.3063m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.750, Y:-5.375, Z:-1.903
速度因子: 0.637, 自适应速度因子: 0.637
最终轴输出: X=-0.254, Y=-0.352, Z=0.005, RotY=0.000
==================================================

=== ROV控制状态 Frame 1180 Time 23.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.225, 9.052, 3.212)
位置误差向量: (0.310, 0.475, 0.128), 距离: 0.5812m
当前速度: (0.1832, 0.1761, 0.0849), 速度大小: 0.2680m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.260, Y:-4.847, Z:-1.672
速度因子: 0.581, 自适应速度因子: 0.581
最终轴输出: X=-0.204, Y=-0.289, Z=0.003, RotY=0.000
==================================================

=== ROV控制状态 Frame 1190 Time 23.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.259, 9.085, 3.227)
位置误差向量: (0.276, 0.442, 0.113), 距离: 0.5328m
当前速度: (0.1584, 0.1568, 0.0744), 速度大小: 0.2350m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.860, Y:-4.403, Z:-1.481
速度因子: 0.533, 自适应速度因子: 0.533
最终轴输出: X=-0.165, Y=-0.239, Z=0.002, RotY=0.000
==================================================

=== ROV控制状态 Frame 1200 Time 24.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.288, 9.115, 3.241)
位置误差向量: (0.246, 0.412, 0.099), 距离: 0.4904m
当前速度: (0.1374, 0.1399, 0.0654), 速度大小: 0.2067m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-2.529, Y:-4.027, Z:-1.322
速度因子: 0.490, 自适应速度因子: 0.490
最终轴输出: X=-0.135, Y=-0.201, Z=0.002, RotY=0.000
==================================================

=== ROV控制状态 Frame 1210 Time 24.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.314, 9.141, 3.253)
位置误差向量: (0.221, 0.386, 0.087), 距离: 0.4531m
当前速度: (0.1198, 0.1250, 0.0577), 速度大小: 0.1825m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-2.255, Y:-3.707, Z:-1.188
速度因子: 0.453, 自适应速度因子: 0.453
最终轴输出: X=-0.112, Y=-0.170, Z=0.001, RotY=0.000
==================================================

=== ROV控制状态 Frame 1220 Time 24.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.336, 9.164, 3.264)
位置误差向量: (0.199, 0.363, 0.076), 距离: 0.4202m
当前速度: (0.1048, 0.1120, 0.0511), 速度大小: 0.1617m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-2.026, Y:-3.433, Z:-1.075
速度因子: 0.420, 自适应速度因子: 0.420
最终轴输出: X=-0.094, Y=-0.146, Z=0.001, RotY=0.000
==================================================

=== ROV控制状态 Frame 1230 Time 24.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.355, 9.186, 3.274)
位置误差向量: (0.179, 0.341, 0.066), 距离: 0.3911m
当前速度: (0.0921, 0.1006, 0.0455), 速度大小: 0.1438m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-1.833, Y:-3.197, Z:-0.979
速度因子: 0.391, 自适应速度因子: 0.391
最终轴输出: X=-0.079, Y=-0.126, Z=0.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 1240 Time 24.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.373, 9.205, 3.282)
位置误差向量: (0.162, 0.322, 0.058), 距离: 0.3653m
当前速度: (0.0812, 0.0906, 0.0405), 速度大小: 0.1282m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-1.669, Y:-2.992, Z:-0.897
速度因子: 0.365, 自适应速度因子: 0.365
最终轴输出: X=-0.068, Y=-0.110, Z=0.000, RotY=0.000
==================================================

=== ROV控制状态 Frame 1250 Time 25.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.388, 9.222, 3.290)
位置误差向量: (0.147, 0.305, 0.050), 距离: 0.3423m
当前速度: (0.0718, 0.0819, 0.0362), 速度大小: 0.1148m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-1.529, Y:-2.814, Z:-0.871
速度因子: 0.342, 自适应速度因子: 0.342
最终轴输出: X=-0.059, Y=-0.097, Z=-0.001, RotY=0.000
==================================================

=== ROV控制状态 Frame 1260 Time 25.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.401, 9.237, 3.297)
位置误差向量: (0.133, 0.290, 0.043), 距离: 0.3218m
当前速度: (0.0635, 0.0741, 0.0330), 速度大小: 0.1031m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-1.409, Y:-2.658, Z:-0.804
速度因子: 0.322, 自适应速度因子: 0.322
最终轴输出: X=-0.051, Y=-0.086, Z=-0.001, RotY=0.000
==================================================

=== ROV控制状态 Frame 1270 Time 25.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.413, 9.251, 3.303)
位置误差向量: (0.121, 0.276, 0.037), 距离: 0.3034m
当前速度: (0.0564, 0.0674, 0.0299), 速度大小: 0.0928m/s
控制阶段: 线性减速, 控制模式: PositionPrimary
位置控制: True, 旋转控制: False
PID输出 - X:-1.305, Y:-2.521, Z:-0.746
速度因子: 0.303, 自适应速度因子: 0.303
最终轴输出: X=-0.045, Y=-0.077, Z=-0.001, RotY=0.000
==================================================

=== ROV控制状态 Frame 1280 Time 25.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.424, 9.265, 3.309)
位置误差向量: (0.110, 0.262, 0.031), 距离: 0.2859m
当前速度: (0.0571, 0.0696, 0.0314), 速度大小: 0.0954m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-2.422, Y:-4.778, Z:-1.394
速度因子: 0.289, 自适应速度因子: 0.289
最终轴输出: X=-0.080, Y=-0.139, Z=-0.002, RotY=-0.289
==================================================

=== ROV控制状态 Frame 1290 Time 25.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.436, 9.279, 3.316)
位置误差向量: (0.099, 0.248, 0.024), 距离: 0.2681m
当前速度: (0.0552, 0.0703, 0.0309), 速度大小: 0.0946m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-2.230, Y:-4.504, Z:-1.286
速度因子: 0.274, 自适应速度因子: 0.274
最终轴输出: X=-0.070, Y=-0.124, Z=-0.003, RotY=-0.274
==================================================

=== ROV控制状态 Frame 1300 Time 26.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.446, 9.293, 3.322)
位置误差向量: (0.088, 0.234, 0.018), 距离: 0.2509m
当前速度: (0.0503, 0.0683, 0.0291), 速度大小: 0.0897m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-2.049, Y:-4.238, Z:-1.180
速度因子: 0.261, 自适应速度因子: 0.261
最终轴输出: X=-0.061, Y=-0.111, Z=-0.004, RotY=-0.261
==================================================

=== ROV控制状态 Frame 1310 Time 26.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.456, 9.306, 3.327)
位置误差向量: (0.079, 0.221, 0.013), 距离: 0.2348m
当前速度: (0.0446, 0.0648, 0.0268), 速度大小: 0.0832m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.887, Y:-3.989, Z:-1.082
速度因子: 0.248, 自适应速度因子: 0.248
最终轴输出: X=-0.053, Y=-0.099, Z=-0.004, RotY=-0.248
==================================================

=== ROV控制状态 Frame 1320 Time 26.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.464, 9.319, 3.332)
位置误差向量: (0.071, 0.208, 0.008), 距离: 0.2201m
当前速度: (0.0392, 0.0607, 0.0246), 速度大小: 0.0763m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.746, Y:-3.761, Z:-0.995
速度因子: 0.236, 自适应速度因子: 0.236
最终轴输出: X=-0.047, Y=-0.089, Z=-0.005, RotY=-0.236
==================================================

=== ROV控制状态 Frame 1330 Time 26.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.471, 9.330, 3.337)
位置误差向量: (0.063, 0.197, 0.003), 距离: 0.2066m
当前速度: (0.0346, 0.0564, 0.0228), 速度大小: 0.0700m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.625, Y:-3.556, Z:-0.918
速度因子: 0.225, 自适应速度因子: 0.225
最终轴输出: X=-0.041, Y=-0.080, Z=-0.005, RotY=-0.225
==================================================

=== ROV控制状态 Frame 1340 Time 26.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.478, 9.341, 3.341)
位置误差向量: (0.057, 0.186, -0.001), 距离: 0.1943m
当前速度: (0.0306, 0.0522, 0.0211), 速度大小: 0.0641m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.520, Y:-3.370, Z:-0.849
速度因子: 0.215, 自适应速度因子: 0.215
最终轴输出: X=-0.037, Y=-0.073, Z=-0.005, RotY=-0.215
==================================================

=== ROV控制状态 Frame 1350 Time 27.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.484, 9.351, 3.345)
位置误差向量: (0.051, 0.176, -0.005), 距离: 0.1831m
当前速度: (0.0275, 0.0483, 0.0197), 速度大小: 0.0589m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.430, Y:-3.204, Z:-0.787
速度因子: 0.207, 自适应速度因子: 0.207
最终轴输出: X=-0.033, Y=-0.066, Z=-0.005, RotY=-0.207
==================================================

=== ROV控制状态 Frame 1360 Time 27.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.489, 9.360, 3.349)
位置误差向量: (0.046, 0.167, -0.009), 距离: 0.1730m
当前速度: (0.0250, 0.0446, 0.0184), 速度大小: 0.0544m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.400, Y:-3.054, Z:-0.732
速度因子: 0.198, 自适应速度因子: 0.198
最终轴输出: X=-0.031, Y=-0.061, Z=-0.005, RotY=-0.198
==================================================

=== ROV控制状态 Frame 1370 Time 27.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.493, 9.369, 3.353)
位置误差向量: (0.041, 0.158, -0.013), 距离: 0.1638m
当前速度: (0.0229, 0.0413, 0.0172), 速度大小: 0.0502m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.325, Y:-2.919, Z:-0.681
速度因子: 0.191, 自适应速度因子: 0.191
最终轴输出: X=-0.028, Y=-0.056, Z=-0.005, RotY=-0.191
==================================================

=== ROV控制状态 Frame 1380 Time 27.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.498, 9.377, 3.356)
位置误差向量: (0.037, 0.150, -0.016), 距离: 0.1553m
当前速度: (0.0211, 0.0382, 0.0160), 速度大小: 0.0465m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.257, Y:-2.798, Z:-0.635
速度因子: 0.184, 自适应速度因子: 0.184
最终轴输出: X=-0.025, Y=-0.052, Z=-0.005, RotY=-0.184
==================================================

=== ROV控制状态 Frame 1390 Time 27.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.502, 9.384, 3.359)
位置误差向量: (0.033, 0.143, -0.019), 距离: 0.1477m
当前速度: (0.0195, 0.0355, 0.0149), 速度大小: 0.0432m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.197, Y:-2.688, Z:-0.594
速度因子: 0.178, 自适应速度因子: 0.178
最终轴输出: X=-0.023, Y=-0.048, Z=-0.005, RotY=-0.178
==================================================

=== ROV控制状态 Frame 1400 Time 28.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.506, 9.391, 3.362)
位置误差向量: (0.029, 0.136, -0.022), 距离: 0.1407m
当前速度: (0.0178, 0.0330, 0.0139), 速度大小: 0.0401m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.142, Y:-2.588, Z:-0.556
速度因子: 0.173, 自适应速度因子: 0.173
最终轴输出: X=-0.021, Y=-0.045, Z=-0.005, RotY=-0.173
==================================================

=== ROV控制状态 Frame 1410 Time 28.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.509, 9.397, 3.365)
位置误差向量: (0.025, 0.130, -0.025), 距离: 0.1343m
当前速度: (0.0164, 0.0309, 0.0130), 速度大小: 0.0373m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.093, Y:-2.498, Z:-0.521
速度因子: 0.167, 自适应速度因子: 0.167
最终轴输出: X=-0.020, Y=-0.042, Z=-0.005, RotY=-0.167
==================================================

=== ROV控制状态 Frame 1420 Time 28.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.512, 9.403, 3.367)
位置误差向量: (0.022, 0.124, -0.027), 距离: 0.1286m
当前速度: (0.0152, 0.0288, 0.0122), 速度大小: 0.0348m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.048, Y:-2.415, Z:-0.490
速度因子: 0.163, 自适应速度因子: 0.163
最终轴输出: X=-0.018, Y=-0.039, Z=-0.005, RotY=-0.163
==================================================

=== ROV控制状态 Frame 1430 Time 28.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.515, 9.409, 3.370)
位置误差向量: (0.019, 0.118, -0.030), 距离: 0.1233m
当前速度: (0.0141, 0.0270, 0.0114), 速度大小: 0.0326m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-1.008, Y:-2.339, Z:-0.461
速度因子: 0.159, 自适应速度因子: 0.159
最终轴输出: X=-0.017, Y=-0.037, Z=-0.004, RotY=-0.159
==================================================

=== ROV控制状态 Frame 1440 Time 28.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.518, 9.414, 3.372)
位置误差向量: (0.017, 0.113, -0.032), 距离: 0.1184m
当前速度: (0.0132, 0.0255, 0.0108), 速度大小: 0.0306m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.972, Y:-2.270, Z:-0.434
速度因子: 0.155, 自适应速度因子: 0.155
最终轴输出: X=-0.016, Y=-0.035, Z=-0.004, RotY=-0.155
==================================================

=== ROV控制状态 Frame 1450 Time 29.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.520, 9.419, 3.374)
位置误差向量: (0.014, 0.108, -0.034), 距离: 0.1140m
当前速度: (0.0123, 0.0240, 0.0102), 速度大小: 0.0288m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.939, Y:-2.206, Z:-0.409
速度因子: 0.151, 自适应速度因子: 0.151
最终轴输出: X=-0.015, Y=-0.033, Z=-0.004, RotY=-0.151
==================================================

=== ROV控制状态 Frame 1460 Time 29.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.523, 9.424, 3.376)
位置误差向量: (0.012, 0.103, -0.036), 距离: 0.1099m
当前速度: (0.0115, 0.0227, 0.0096), 速度大小: 0.0272m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.908, Y:-2.146, Z:-0.387
速度因子: 0.148, 自适应速度因子: 0.148
最终轴输出: X=-0.014, Y=-0.032, Z=-0.004, RotY=-0.148
==================================================

=== ROV控制状态 Frame 1470 Time 29.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.525, 9.428, 3.378)
位置误差向量: (0.010, 0.099, -0.038), 距离: 0.1062m
当前速度: (0.0110, 0.0216, 0.0091), 速度大小: 0.0258m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.880, Y:-2.092, Z:-0.366
速度因子: 0.145, 自适应速度因子: 0.145
最终轴输出: X=-0.013, Y=-0.030, Z=-0.004, RotY=-0.145
==================================================

=== ROV控制状态 Frame 1480 Time 29.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.527, 9.432, 3.379)
位置误差向量: (0.007, 0.095, -0.039), 距离: 0.1028m
当前速度: (0.0104, 0.0205, 0.0086), 速度大小: 0.0245m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.854, Y:-2.040, Z:-0.346
速度因子: 0.142, 自适应速度因子: 0.142
最终轴输出: X=-0.012, Y=-0.029, Z=-0.004, RotY=-0.142
==================================================

=== ROV控制状态 Frame 1490 Time 29.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.529, 9.436, 3.381)
位置误差向量: (0.005, 0.091, -0.041), 距离: 0.0997m
当前速度: (0.0100, 0.0195, 0.0082), 速度大小: 0.0234m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.831, Y:-1.995, Z:-0.328
速度因子: 0.140, 自适应速度因子: 0.140
最终轴输出: X=-0.012, Y=-0.028, Z=-0.004, RotY=-0.140
==================================================

=== ROV控制状态 Frame 1500 Time 30.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.531, 9.440, 3.383)
位置误差向量: (0.003, 0.087, -0.043), 距离: 0.0969m
当前速度: (0.0096, 0.0187, 0.0078), 速度大小: 0.0224m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.820, Y:-1.979, Z:-0.315
速度因子: 0.137, 自适应速度因子: 0.137
最终轴输出: X=-0.011, Y=-0.027, Z=-0.004, RotY=-0.137
==================================================

=== ROV控制状态 Frame 1510 Time 30.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.533, 9.444, 3.384)
位置误差向量: (0.002, 0.083, -0.044), 距离: 0.0943m
当前速度: (0.0092, 0.0180, 0.0075), 速度大小: 0.0215m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.808, Y:-1.961, Z:-0.302
速度因子: 0.135, 自适应速度因子: 0.135
最终轴输出: X=-0.011, Y=-0.027, Z=-0.004, RotY=-0.135
==================================================

=== ROV控制状态 Frame 1520 Time 30.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.535, 9.447, 3.386)
位置误差向量: (0.000, 0.080, -0.046), 距离: 0.0919m
当前速度: (0.0090, 0.0174, 0.0072), 速度大小: 0.0208m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.796, Y:-1.942, Z:-0.290
速度因子: 0.134, 自适应速度因子: 0.134
最终轴输出: X=-0.011, Y=-0.026, Z=-0.004, RotY=-0.134
==================================================

=== ROV控制状态 Frame 1530 Time 30.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.537, 9.451, 3.387)
位置误差向量: (-0.002, 0.076, -0.047), 距离: 0.0897m
当前速度: (0.0088, 0.0168, 0.0070), 速度大小: 0.0202m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.785, Y:-1.923, Z:-0.278
速度因子: 0.132, 自适应速度因子: 0.132
最终轴输出: X=-0.010, Y=-0.025, Z=-0.004, RotY=-0.132
==================================================

=== ROV控制状态 Frame 1540 Time 30.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.538, 9.454, 3.389)
位置误差向量: (-0.004, 0.073, -0.049), 距离: 0.0877m
当前速度: (0.0086, 0.0164, 0.0067), 速度大小: 0.0197m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.773, Y:-1.904, Z:-0.266
速度因子: 0.130, 自适应速度因子: 0.130
最终轴输出: X=-0.010, Y=-0.025, Z=-0.004, RotY=-0.130
==================================================

=== ROV控制状态 Frame 1550 Time 31.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.540, 9.457, 3.390)
位置误差向量: (-0.005, 0.070, -0.050), 距离: 0.0859m
当前速度: (0.0083, 0.0160, 0.0065), 速度大小: 0.0191m/s
控制阶段: 精细控制, 控制模更新了日志，可是目前还是过量移动呀式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.760, Y:-1.884, Z:-0.254
速度因子: 0.129, 自适应速度因子: 0.129
最终轴输出: X=-0.010, Y=-0.024, Z=-0.004, RotY=-0.129
==================================================

=== ROV控制状态 Frame 1560 Time 31.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.542, 9.460, 3.391)
位置误差向量: (-0.007, 0.067, -0.051), 距离: 0.0843m
当前速度: (0.0081, 0.0155, 0.0063), 速度大小: 0.0186m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.748, Y:-1.863, Z:-0.231
速度因子: 0.127, 自适应速度因子: 0.127
最终轴输出: X=-0.009, Y=-0.024, Z=-0.003, RotY=-0.127
==================================================

=== ROV控制状态 Frame 1570 Time 31.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.543, 9.463, 3.392)
位置误差向量: (-0.009, 0.064, -0.052), 距离: 0.0828m
当前速度: (0.0080, 0.0152, 0.0060), 速度大小: 0.0182m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.736, Y:-1.842, Z:-0.220
速度因子: 0.126, 自适应速度因子: 0.126
最终轴输出: X=-0.009, Y=-0.023, Z=-0.003, RotY=-0.126
==================================================

=== ROV控制状态 Frame 1580 Time 31.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.545, 9.466, 3.394)
位置误差向量: (-0.010, 0.061, -0.054), 距离: 0.0815m
当前速度: (0.0078, 0.0148, 0.0058), 速度大小: 0.0177m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.723, Y:-1.820, Z:-0.209
速度因子: 0.125, 自适应速度因子: 0.125
最终轴输出: X=-0.009, Y=-0.023, Z=-0.003, RotY=-0.125
==================================================

=== ROV控制状态 Frame 1590 Time 31.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.546, 9.469, 3.395)
位置误差向量: (-0.012, 0.058, -0.055), 距离: 0.0803m
当前速度: (0.0076, 0.0145, 0.0056), 速度大小: 0.0173m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.710, Y:-1.798, Z:-0.198
速度因子: 0.124, 自适应速度因子: 0.124
最终轴输出: X=-0.009, Y=-0.022, Z=-0.003, RotY=-0.124
==================================================

=== ROV控制状态 Frame 1600 Time 32.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.548, 9.472, 3.396)
位置误差向量: (-0.013, 0.055, -0.056), 距离: 0.0793m
当前速度: (0.0074, 0.0142, 0.0054), 速度大小: 0.0169m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.697, Y:-1.776, Z:-0.188
速度因子: 0.123, 自适应速度因子: 0.123
最终轴输出: X=-0.008, Y=-0.022, Z=-0.003, RotY=-0.123
==================================================

=== ROV控制状态 Frame 1610 Time 32.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.549, 9.475, 3.397)
位置误差向量: (-0.015, 0.052, -0.057), 距离: 0.0784m
当前速度: (0.0073, 0.0139, 0.0052), 速度大小: 0.0165m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.684, Y:-1.754, Z:-0.178
速度因子: 0.123, 自适应速度因子: 0.123
最终轴输出: X=-0.008, Y=-0.022, Z=-0.003, RotY=-0.123
==================================================

=== ROV控制状态 Frame 1620 Time 32.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.551, 9.478, 3.398)
位置误差向量: (-0.016, 0.049, -0.058), 距离: 0.0776m
当前速度: (0.0072, 0.0136, 0.0050), 速度大小: 0.0162m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.672, Y:-1.757, Z:-0.168
速度因子: 0.122, 自适应速度因子: 0.122
最终轴输出: X=-0.008, Y=-0.021, Z=-0.003, RotY=-0.122
==================================================

=== ROV控制状态 Frame 1630 Time 32.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.552, 9.481, 3.399)
位置误差向量: (-0.018, 0.046, -0.059), 距离: 0.0770m
当前速度: (0.0071, 0.0134, 0.0049), 速度大小: 0.0159m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.658, Y:-1.734, Z:-0.158
速度因子: 0.122, 自适应速度因子: 0.122
最终轴输出: X=-0.008, Y=-0.021, Z=-0.003, RotY=-0.122
==================================================

=== ROV控制状态 Frame 1640 Time 32.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.554, 9.483, 3.400)
位置误差向量: (-0.019, 0.044, -0.060), 距离: 0.0765m
当前速度: (0.0071, 0.0132, 0.0047), 速度大小: 0.0157m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.646, Y:-1.710, Z:-0.149
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.007, Y=-0.021, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1650 Time 33.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.555, 9.486, 3.401)
位置误差向量: (-0.021, 0.041, -0.061), 距离: 0.0762m
当前速度: (0.0069, 0.0130, 0.0046), 速度大小: 0.0154m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.633, Y:-1.687, Z:-0.140
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.007, Y=-0.020, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1660 Time 33.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.556, 9.488, 3.402)
位置误差向量: (-0.022, 0.039, -0.062), 距离: 0.0759m
当前速度: (0.0068, 0.0128, 0.0044), 速度大小: 0.0151m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.620, Y:-1.663, Z:-0.131
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.007, Y=-0.020, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1670 Time 33.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.558, 9.491, 3.402)
位置误差向量: (-0.023, 0.036, -0.062), 距离: 0.0758m
当前速度: (0.0067, 0.0126, 0.0043), 速度大小: 0.0149m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.607, Y:-1.639, Z:-0.122
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.007, Y=-0.020, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1680 Time 33.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.559, 9.493, 3.403)
位置误差向量: (-0.025, 0.034, -0.063), 距离: 0.0758m
当前速度: (0.0066, 0.0124, 0.0042), 速度大小: 0.0146m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.594, Y:-1.615, Z:-0.113
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.007, Y=-0.019, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1690 Time 33.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.560, 9.496, 3.404)
位置误差向量: (-0.026, 0.031, -0.064), 距离: 0.0758m
当前速度: (0.0065, 0.0122, 0.0040), 速度大小: 0.0144m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.581, Y:-1.591, Z:-0.105
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.006, Y=-0.019, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1700 Time 34.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.562, 9.498, 3.405)
位置误差向量: (-0.027, 0.029, -0.065), 距离: 0.0760m
当前速度: (0.0064, 0.0120, 0.0039), 速度大小: 0.0142m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.568, Y:-1.567, Z:-0.097
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.006, Y=-0.019, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1710 Time 34.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.563, 9.501, 3.406)
位置误差向量: (-0.028, 0.026, -0.066), 距离: 0.0762m
当前速度: (0.0063, 0.0118, 0.0038), 速度大小: 0.0139m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.555, Y:-1.543, Z:-0.089
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.006, Y=-0.019, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1720 Time 34.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.564, 9.503, 3.406)
位置误差向量: (-0.030, 0.024, -0.066), 距离: 0.0766m
当前速度: (0.0062, 0.0117, 0.0037), 速度大小: 0.0137m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.543, Y:-1.519, Z:-0.081
速度因子: 0.121, 自适应速度因子: 0.121
最终轴输出: X=-0.006, Y=-0.018, Z=-0.003, RotY=-0.121
==================================================

=== ROV控制状态 Frame 1730 Time 34.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.565, 9.505, 3.407)
位置误差向量: (-0.031, 0.022, -0.067), 距离: 0.0770m
当前速度: (0.0060, 0.0115, 0.0035), 速度大小: 0.0135m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.530, Y:-1.495, Z:-0.074
速度因子: 0.122, 自适应速度因子: 0.122
最终轴输出: X=-0.006, Y=-0.018, Z=-0.003, RotY=-0.122
==================================================

=== ROV控制状态 Frame 1740 Time 34.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.567, 9.508, 3.408)
位置误差向量: (-0.032, 0.019, -0.068), 距离: 0.0775m
当前速度: (0.0060, 0.0113, 0.0034), 速度大小: 0.0133m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.518, Y:-1.471, Z:-0.067
速度因子: 0.122, 自适应速度因子: 0.122
最终轴输出: X=-0.006, Y=-0.018, Z=-0.003, RotY=-0.122
==================================================

=== ROV控制状态 Frame 1750 Time 35.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.568, 9.510, 3.409)
位置误差向量: (-0.033, 0.017, -0.069), 距离: 0.0781m
当前速度: (0.0059, 0.0112, 0.0033), 速度大小: 0.0131m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.505, Y:-1.448, Z:-0.060
速度因子: 0.122, 自适应速度因子: 0.122
最终轴输出: X=-0.006, Y=-0.018, Z=-0.003, RotY=-0.122
==================================================

=== ROV控制状态 Frame 1760 Time 35.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.569, 9.512, 3.409)
位置误差向量: (-0.034, 0.015, -0.069), 距离: 0.0787m
当前速度: (0.0059, 0.0111, 0.0032), 速度大小: 0.0129m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.493, Y:-1.424, Z:-0.053
速度因子: 0.123, 自适应速度因子: 0.123
最终轴输出: X=-0.005, Y=-0.018, Z=-0.003, RotY=-0.123
==================================================

=== ROV控制状态 Frame 1770 Time 35.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.570, 9.514, 3.410)
位置误差向量: (-0.036, 0.013, -0.070), 距离: 0.0794m
当前速度: (0.0057, 0.0109, 0.0031), 速度大小: 0.0127m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.481, Y:-1.401, Z:-0.046
速度因子: 0.123, 自适应速度因子: 0.123
最终轴输出: X=-0.005, Y=-0.017, Z=-0.003, RotY=-0.123
==================================================

=== ROV控制状态 Frame 1780 Time 35.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.571, 9.516, 3.410)
位置误差向量: (-0.037, 0.011, -0.070), 距离: 0.0801m
当前速度: (0.0057, 0.0107, 0.0030), 速度大小: 0.0125m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.469, Y:-1.377, Z:-0.040
速度因子: 0.124, 自适应速度因子: 0.124
最终轴输出: X=-0.005, Y=-0.017, Z=-0.003, RotY=-0.124
==================================================

=== ROV控制状态 Frame 1790 Time 35.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.572, 9.519, 3.411)
位置误差向量: (-0.038, 0.008, -0.071), 距离: 0.0809m
当前速度: (0.0055, 0.0106, 0.0029), 速度大小: 0.0123m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.456, Y:-1.354, Z:-0.034
速度因子: 0.125, 自适应速度因子: 0.125
最终轴输出: X=-0.005, Y=-0.017, Z=-0.003, RotY=-0.125
==================================================

=== ROV控制状态 Frame 1800 Time 36.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.573, 9.521, 3.412)
位置误差向量: (-0.039, 0.006, -0.072), 距离: 0.0817m
当前速度: (0.0055, 0.0104, 0.0028), 速度大小: 0.0121m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.445, Y:-1.331, Z:-0.028
速度因子: 0.125, 自适应速度因子: 0.125
最终轴输出: X=-0.005, Y=-0.017, Z=-0.003, RotY=-0.125
==================================================

=== ROV控制状态 Frame 1810 Time 36.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.575, 9.523, 3.412)
位置误差向量: (-0.040, 0.004, -0.072), 距离: 0.0826m
当前速度: (0.0054, 0.0103, 0.0027), 速度大小: 0.0120m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.433, Y:-1.308, Z:-0.022
速度因子: 0.126, 自适应速度因子: 0.126
最终轴输出: X=-0.005, Y=-0.016, Z=-0.003, RotY=-0.126
==================================================

=== ROV控制状态 Frame 1820 Time 36.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.576, 9.525, 3.413)
位置误差向量: (-0.041, 0.002, -0.073), 距离: 0.0835m
当前速度: (0.0053, 0.0103, 0.0026), 速度大小: 0.0118m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.421, Y:-1.286, Z:-0.017
速度因子: 0.127, 自适应速度因子: 0.127
最终轴输出: X=-0.005, Y=-0.016, Z=-0.002, RotY=-0.127
==================================================

=== ROV控制状态 Frame 1830 Time 36.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.577, 9.527, 3.413)
位置误差向量: (-0.042, 0.000, -0.073), 距离: 0.0844m
当前速度: (0.0053, 0.0101, 0.0025), 速度大小: 0.0117m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.410, Y:-1.263, Z:-0.012
速度因子: 0.128, 自适应速度因子: 0.128
最终轴输出: X=-0.005, Y=-0.016, Z=-0.002, RotY=-0.128
==================================================

=== ROV控制状态 Frame 1840 Time 36.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.578, 9.529, 3.414)
位置误差向量: (-0.043, -0.002, -0.074), 距离: 0.0854m
当前速度: (0.0052, 0.0100, 0.0023), 速度大小: 0.0115m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.399, Y:-1.241, Z:-0.007
速度因子: 0.128, 自适应速度因子: 0.128
最终轴输出: X=-0.004, Y=-0.016, Z=-0.002, RotY=-0.128
==================================================

=== ROV控制状态 Frame 1850 Time 37.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.579, 9.531, 3.414)
位置误差向量: (-0.044, -0.004, -0.074), 距离: 0.0864m
当前速度: (0.0051, 0.0098, 0.0023), 速度大小: 0.0113m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.388, Y:-1.219, Z:-0.002
速度因子: 0.129, 自适应速度因子: 0.129
最终轴输出: X=-0.004, Y=-0.016, Z=-0.002, RotY=-0.129
==================================================

=== ROV控制状态 Frame 1860 Time 37.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.580, 9.533, 3.414)
位置误差向量: (-0.045, -0.006, -0.074), 距离: 0.0874m
当前速度: (0.0051, 0.0097, 0.0022), 速度大小: 0.0111m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.377, Y:-1.197, Z:0.003
速度因子: 0.130, 自适应速度因子: 0.130
最终轴输出: X=-0.004, Y=-0.016, Z=-0.002, RotY=-0.130
==================================================

=== ROV控制状态 Frame 1870 Time 37.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.581, 9.535, 3.415)
位置误差向量: (-0.046, -0.008, -0.075), 距离: 0.0884m
当前速度: (0.0050, 0.0096, 0.0021), 速度大小: 0.0110m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.366, Y:-1.175, Z:0.007
速度因子: 0.131, 自适应速度因子: 0.131
最终轴输出: X=-0.004, Y=-0.015, Z=-0.002, RotY=-0.131
==================================================

=== ROV控制状态 Frame 1880 Time 37.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.582, 9.537, 3.415)
位置误差向量: (-0.047, -0.010, -0.075), 距离: 0.0894m
当前速度: (0.0049, 0.0094, 0.0020), 速度大小: 0.0108m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.355, Y:-1.154, Z:0.011
速度因子: 0.132, 自适应速度因子: 0.132
最终轴输出: X=-0.004, Y=-0.015, Z=-0.002, RotY=-0.132
==================================================

=== ROV控制状态 Frame 1890 Time 37.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.583, 9.538, 3.416)
位置误差向量: (-0.048, -0.012, -0.076), 距离: 0.0905m
当前速度: (0.0048, 0.0093, 0.0019), 速度大小: 0.0107m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.345, Y:-1.133, Z:0.015
速度因子: 0.132, 自适应速度因子: 0.132
最终轴输出: X=-0.004, Y=-0.015, Z=-0.002, RotY=-0.132
==================================================

=== ROV控制状态 Frame 1900 Time 38.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.584, 9.540, 3.416)
位置误差向量: (-0.049, -0.013, -0.076), 距离: 0.0916m
当前速度: (0.0048, 0.0092, 0.0018), 速度大小: 0.0105m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.335, Y:-1.112, Z:0.019
速度因子: 0.133, 自适应速度因子: 0.133
最终轴输出: X=-0.004, Y=-0.015, Z=-0.002, RotY=-0.133
==================================================

=== ROV控制状态 Frame 1910 Time 38.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.585, 9.542, 3.416)
位置误差向量: (-0.050, -0.015, -0.076), 距离: 0.0926m
当前速度: (0.0047, 0.0091, 0.0017), 速度大小: 0.0104m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.317, Y:-1.092, Z:0.023
速度因子: 0.134, 自适应速度因子: 0.134
最终轴输出: X=-0.004, Y=-0.015, Z=-0.002, RotY=-0.134
==================================================

=== ROV控制状态 Frame 1920 Time 38.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.585, 9.544, 3.417)
位置误差向量: (-0.051, -0.017, -0.077), 距离: 0.0937m
当前速度: (0.0046, 0.0090, 0.0016), 速度大小: 0.0102m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.307, Y:-1.071, Z:0.026
速度因子: 0.135, 自适应速度因子: 0.135
最终轴输出: X=-0.004, Y=-0.014, Z=-0.002, RotY=-0.135
==================================================

=== ROV控制状态 Frame 1930 Time 38.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.586, 9.546, 3.417)
位置误差向量: (-0.052, -0.019, -0.077), 距离: 0.0948m
当前速度: (0.0045, 0.0089, 0.0015), 速度大小: 0.0101m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.297, Y:-1.051, Z:0.029
速度因子: 0.136, 自适应速度因子: 0.136
最终轴输出: X=-0.004, Y=-0.014, Z=-0.002, RotY=-0.136
==================================================

=== ROV控制状态 Frame 1940 Time 38.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.587, 9.548, 3.417)
位置误差向量: (-0.053, -0.021, -0.077), 距离: 0.0959m
当前速度: (0.0043, 0.0088, 0.0014), 速度大小: 0.0099m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.288, Y:-1.032, Z:0.033
速度因子: 0.137, 自适应速度因子: 0.137
最终轴输出: X=-0.003, Y=-0.014, Z=-0.002, RotY=-0.137
==================================================

=== ROV控制状态 Frame 1950 Time 39.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.588, 9.549, 3.418)
位置误差向量: (-0.054, -0.022, -0.078), 距离: 0.0969m
当前速度: (0.0042, 0.0086, 0.0013), 速度大小: 0.0097m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.279, Y:-1.012, Z:0.035
速度因子: 0.138, 自适应速度因子: 0.138
最终轴输出: X=-0.003, Y=-0.014, Z=-0.002, RotY=-0.138
==================================================

=== ROV控制状态 Frame 1960 Time 39.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.589, 9.551, 3.418)
位置误差向量: (-0.055, -0.024, -0.078), 距离: 0.0980m
当前速度: (0.0041, 0.0085, 0.0012), 速度大小: 0.0095m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.270, Y:-0.993, Z:0.038
速度因子: 0.138, 自适应速度因子: 0.138
最终轴输出: X=-0.003, Y=-0.014, Z=-0.002, RotY=-0.138
==================================================

=== ROV控制状态 Frame 1970 Time 39.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.590, 9.553, 3.418)
位置误差向量: (-0.055, -0.026, -0.078), 距离: 0.0991m
当前速度: (0.0040, 0.0084, 0.0011), 速度大小: 0.0094m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.262, Y:-0.974, Z:0.041
速度因子: 0.139, 自适应速度因子: 0.139
最终轴输出: X=-0.003, Y=-0.014, Z=-0.002, RotY=-0.139
==================================================

=== ROV控制状态 Frame 1980 Time 39.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.591, 9.554, 3.418)
位置误差向量: (-0.056, -0.027, -0.078), 距离: 0.1002m
当前速度: (0.0040, 0.0083, 0.0011), 速度大小: 0.0093m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.254, Y:-0.957, Z:0.043
速度因子: 0.140, 自适应速度因子: 0.140
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.140
==================================================

=== ROV控制状态 Frame 1990 Time 39.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.591, 9.556, 3.419)
位置误差向量: (-0.057, -0.029, -0.079), 距离: 0.1012m
当前速度: (0.0039, 0.0082, 0.0010), 速度大小: 0.0091m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.247, Y:-0.943, Z:0.045
速度因子: 0.141, 自适应速度因子: 0.141
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.141
==================================================

=== ROV控制状态 Frame 2000 Time 40.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.592, 9.558, 3.419)
位置误差向量: (-0.058, -0.031, -0.079), 距离: 0.1023m
当前速度: (0.0039, 0.0081, 0.0009), 速度大小: 0.0090m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.240, Y:-0.930, Z:0.048
速度因子: 0.142, 自适应速度因子: 0.142
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.142
==================================================

=== ROV控制状态 Frame 2010 Time 40.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.593, 9.559, 3.419)
位置误差向量: (-0.058, -0.032, -0.079), 距离: 0.1033m
当前速度: (0.0038, 0.0080, 0.0008), 速度大小: 0.0089m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.233, Y:-0.918, Z:0.050
速度因子: 0.143, 自适应速度因子: 0.143
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.143
==================================================

=== ROV控制状态 Frame 2020 Time 40.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.594, 9.561, 3.419)
位置误差向量: (-0.059, -0.034, -0.079), 距离: 0.1044m
当前速度: (0.0037, 0.0079, 0.0008), 速度大小: 0.0088m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.226, Y:-0.905, Z:0.052
速度因子: 0.144, 自适应速度因子: 0.144
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.144
==================================================

=== ROV控制状态 Frame 2030 Time 40.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.594, 9.562, 3.419)
位置误差向量: (-0.060, -0.035, -0.079), 距离: 0.1054m
当前速度: (0.0037, 0.0079, 0.0007), 速度大小: 0.0087m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.220, Y:-0.892, Z:0.054
速度因子: 0.144, 自适应速度因子: 0.144
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=-0.055
==================================================

=== ROV控制状态 Frame 2040 Time 40.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.595, 9.564, 3.419)
位置误差向量: (-0.061, -0.037, -0.079), 距离: 0.1065m
当前速度: (0.0037, 0.0079, 0.0006), 速度大小: 0.0087m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.214, Y:-0.880, Z:0.056
速度因子: 0.145, 自适应速度因子: 0.145
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=0.035
==================================================

=== ROV控制状态 Frame 2050 Time 41.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.596, 9.566, 3.419)
位置误差向量: (-0.061, -0.039, -0.079), 距离: 0.1076m
当前速度: (0.0037, 0.0079, 0.0006), 速度大小: 0.0088m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.207, Y:-0.867, Z:0.057
速度因子: 0.146, 自适应速度因子: 0.146
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=0.104
==================================================

=== ROV控制状态 Frame 2060 Time 41.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.597, 9.567, 3.420)
位置误差向量: (-0.062, -0.040, -0.080), 距离: 0.1087m
当前速度: (0.0039, 0.0081, 0.0005), 速度大小: 0.0090m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.201, Y:-0.855, Z:0.059
速度因子: 0.147, 自适应速度因子: 0.147
最终轴输出: X=-0.003, Y=-0.013, Z=-0.002, RotY=0.138
==================================================

=== ROV控制状态 Frame 2070 Time 41.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.597, 9.569, 3.420)
位置误差向量: (-0.063, -0.042, -0.080), 距离: 0.1098m
当前速度: (0.0039, 0.0082, 0.0004), 速度大小: 0.0091m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.194, Y:-0.842, Z:0.060
速度因子: 0.148, 自适应速度因子: 0.148
最终轴输出: X=-0.003, Y=-0.012, Z=-0.001, RotY=0.133
==================================================

=== ROV控制状态 Frame 2080 Time 41.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.598, 9.570, 3.420)
位置误差向量: (-0.064, -0.043, -0.080), 距离: 0.1109m
当前速度: (0.0037, 0.0081, 0.0004), 速度大小: 0.0089m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.187, Y:-0.829, Z:0.062
速度因子: 0.149, 自适应速度因子: 0.149
最终轴输出: X=-0.003, Y=-0.012, Z=-0.001, RotY=0.093
==================================================

=== ROV控制状态 Frame 2090 Time 41.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.599, 9.572, 3.420)
位置误差向量: (-0.064, -0.045, -0.080), 距离: 0.1120m
当前速度: (0.0035, 0.0080, 0.0004), 速度大小: 0.0088m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.180, Y:-0.815, Z:0.063
速度因子: 0.150, 自适应速度因子: 0.150
最终轴输出: X=-0.003, Y=-0.012, Z=-0.001, RotY=0.033
==================================================

=== ROV控制状态 Frame 2100 Time 42.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.600, 9.574, 3.420)
位置误差向量: (-0.065, -0.047, -0.080), 距离: 0.1131m
当前速度: (0.0034, 0.0080, 0.0003), 速度大小: 0.0087m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.173, Y:-0.802, Z:0.064
速度因子: 0.151, 自适应速度因子: 0.151
最终轴输出: X=-0.003, Y=-0.012, Z=-0.001, RotY=-0.028
==================================================

=== ROV控制状态 Frame 2110 Time 42.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.600, 9.575, 3.420)
位置误差向量: (-0.066, -0.048, -0.080), 距离: 0.1142m
当前速度: (0.0033, 0.0080, 0.0003), 速度大小: 0.0087m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.167, Y:-0.789, Z:0.066
速度因子: 0.151, 自适应速度因子: 0.151
最终轴输出: X=-0.002, Y=-0.012, Z=-0.001, RotY=-0.075
==================================================

=== ROV控制状态 Frame 2120 Time 42.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.601, 9.577, 3.420)
位置误差向量: (-0.066, -0.050, -0.080), 距离: 0.1153m
当前速度: (0.0033, 0.0081, 0.0001), 速度大小: 0.0087m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.161, Y:-0.776, Z:0.067
速度因子: 0.152, 自适应速度因子: 0.152
最终轴输出: X=-0.002, Y=-0.012, Z=-0.001, RotY=-0.098
==================================================

=== ROV控制状态 Frame 2130 Time 42.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.602, 9.578, 3.420)
位置误差向量: (-0.067, -0.051, -0.080), 距离: 0.1164m
当前速度: (0.0032, 0.0080, 0.0000), 速度大小: 0.0086m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.155, Y:-0.748, Z:0.068
速度因子: 0.153, 自适应速度因子: 0.153
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=-0.092
==================================================

=== ROV控制状态 Frame 2140 Time 42.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.602, 9.580, 3.420)
位置误差向量: (-0.068, -0.053, -0.080), 距离: 0.1175m
当前速度: (0.0031, 0.0079, 0.0000), 速度大小: 0.0085m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.149, Y:-0.734, Z:0.068
速度因子: 0.154, 自适应速度因子: 0.154
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=-0.062
==================================================

=== ROV控制状态 Frame 2150 Time 43.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.603, 9.582, 3.420)
位置误差向量: (-0.068, -0.055, -0.080), 距离: 0.1186m
当前速度: (0.0030, 0.0078, -0.0001), 速度大小: 0.0083m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.143, Y:-0.721, Z:0.069
速度因子: 0.155, 自适应速度因子: 0.155
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=-0.018
==================================================

=== ROV控制状态 Frame 2160 Time 43.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.603, 9.583, 3.420)
位置误差向量: (-0.069, -0.056, -0.080), 距离: 0.1196m
当前速度: (0.0029, 0.0077, -0.0002), 速度大小: 0.0082m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.137, Y:-0.708, Z:0.069
速度因子: 0.156, 自适应速度因子: 0.156
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=0.028
==================================================

=== ROV控制状态 Frame 2170 Time 43.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.604, 9.585, 3.420)
位置误差向量: (-0.070, -0.058, -0.080), 距离: 0.1206m
当前速度: (0.0029, 0.0076, -0.0002), 速度大小: 0.0082m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.132, Y:-0.695, Z:0.070
速度因子: 0.156, 自适应速度因子: 0.156
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=0.063
==================================================

=== ROV控制状态 Frame 2180 Time 43.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.605, 9.586, 3.420)
位置误差向量: (-0.070, -0.059, -0.080), 距离: 0.1216m
当前速度: (0.0028, 0.0076, -0.0003), 速度大小: 0.0081m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.126, Y:-0.682, Z:0.070
速度因子: 0.157, 自适应速度因子: 0.157
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=0.077
==================================================

=== ROV控制状态 Frame 2190 Time 43.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.605, 9.588, 3.420)
位置误差向量: (-0.071, -0.061, -0.080), 距离: 0.1227m
当前速度: (0.0027, 0.0075, -0.0004), 速度大小: 0.0080m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.121, Y:-0.668, Z:0.070
速度因子: 0.158, 自适应速度因子: 0.158
最终轴输出: X=-0.002, Y=-0.011, Z=-0.001, RotY=0.069
==================================================

=== ROV控制状态 Frame 2200 Time 44.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.606, 9.589, 3.420)
位置误差向量: (-0.071, -0.062, -0.080), 距离: 0.1237m
当前速度: (0.0026, 0.0074, -0.0004), 速度大小: 0.0079m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.116, Y:-0.655, Z:0.070
速度因子: 0.159, 自适应速度因子: 0.159
最终轴输出: X=-0.002, Y=-0.010, Z=-0.001, RotY=0.042
==================================================

=== ROV控制状态 Frame 2210 Time 44.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.606, 9.591, 3.420)
位置误差向量: (-0.072, -0.064, -0.080), 距离: 0.1246m
当前速度: (0.0024, 0.0073, -0.0004), 速度大小: 0.0077m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.111, Y:-0.642, Z:0.070
速度因子: 0.160, 自适应速度因子: 0.160
最终轴输出: X=-0.002, Y=-0.010, Z=0.000, RotY=0.006
==================================================

=== ROV控制状态 Frame 2220 Time 44.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.607, 9.592, 3.420)
位置误差向量: (-0.072, -0.065, -0.080), 距离: 0.1256m
当前速度: (0.0023, 0.0072, -0.0005), 速度大小: 0.0076m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.106, Y:-0.629, Z:0.069
速度因子: 0.160, 自适应速度因子: 0.160
最终轴输出: X=-0.002, Y=-0.010, Z=0.000, RotY=-0.032
==================================================

=== ROV控制状态 Frame 2230 Time 44.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.607, 9.594, 3.419)
位置误差向量: (-0.073, -0.067, -0.079), 距离: 0.1265m
当前速度: (0.0022, 0.0072, -0.0005), 速度大小: 0.0075m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.101, Y:-0.617, Z:0.069
速度因子: 0.161, 自适应速度因子: 0.161
最终轴输出: X=-0.002, Y=-0.010, Z=0.000, RotY=-0.058
==================================================

=== ROV控制状态 Frame 2240 Time 44.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.608, 9.595, 3.419)
位置误差向量: (-0.073, -0.068, -0.079), 距离: 0.1275m
当前速度: (0.0021, 0.0071, -0.0006), 速度大小: 0.0074m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.097, Y:-0.604, Z:0.069
速度因子: 0.162, 自适应速度因子: 0.162
最终轴输出: X=-0.002, Y=-0.010, Z=0.000, RotY=-0.066
==================================================

=== ROV控制状态 Frame 2250 Time 45.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.608, 9.596, 3.419)
位置误差向量: (-0.073, -0.069, -0.079), 距离: 0.1284m
当前速度: (0.0020, 0.0070, -0.0007), 速度大小: 0.0073m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.093, Y:-0.591, Z:0.068
速度因子: 0.163, 自适应速度因子: 0.163
最终轴输出: X=-0.002, Y=-0.010, Z=0.000, RotY=-0.054
==================================================

=== ROV控制状态 Frame 2260 Time 45.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.608, 9.598, 3.419)
位置误差向量: (-0.074, -0.071, -0.079), 距离: 0.1293m
当前速度: (0.0019, 0.0069, -0.0007), 速度大小: 0.0072m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.089, Y:-0.579, Z:0.067
速度因子: 0.163, 自适应速度因子: 0.163
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=-0.058
==================================================

=== ROV控制状态 Frame 2270 Time 45.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.609, 9.599, 3.419)
位置误差向量: (-0.074, -0.072, -0.079), 距离: 0.1302m
当前速度: (0.0018, 0.0068, -0.0007), 速度大小: 0.0070m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.085, Y:-0.567, Z:0.067
速度因子: 0.164, 自适应速度因子: 0.164
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=0.005
==================================================

=== ROV控制状态 Frame 2280 Time 45.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.609, 9.601, 3.419)
位置误差向量: (-0.075, -0.074, -0.079), 距离: 0.1310m
当前速度: (0.0018, 0.0067, -0.0008), 速度大小: 0.0070m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.081, Y:-0.554, Z:0.066
速度因子: 0.165, 自适应速度因子: 0.165
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=0.036
==================================================

=== ROV控制状态 Frame 2290 Time 45.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.609, 9.602, 3.419)
位置误差向量: (-0.075, -0.075, -0.079), 距离: 0.1319m
当前速度: (0.0017, 0.0066, -0.0008), 速度大小: 0.0069m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.077, Y:-0.542, Z:0.065
速度因子: 0.166, 自适应速度因子: 0.166
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=0.054
==================================================

=== ROV控制状态 Frame 2300 Time 46.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.610, 9.603, 3.418)
位置误差向量: (-0.075, -0.076, -0.078), 距离: 0.1327m
当前速度: (0.0016, 0.0065, -0.0008), 速度大小: 0.0068m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.074, Y:-0.530, Z:0.064
速度因子: 0.166, 自适应速度因子: 0.166
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=0.056
==================================================

=== ROV控制状态 Frame 2310 Time 46.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.610, 9.604, 3.418)
位置误差向量: (-0.076, -0.077, -0.078), 距离: 0.1335m
当前速度: (0.0015, 0.0064, -0.0008), 速度大小: 0.0067m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.070, Y:-0.518, Z:0.063
速度因子: 0.167, 自适应速度因子: 0.167
最终轴输出: X=-0.002, Y=-0.009, Z=0.000, RotY=0.041
==================================================

=== ROV控制状态 Frame 2320 Time 46.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.610, 9.606, 3.418)
位置误差向量: (-0.076, -0.079, -0.078), 距离: 0.1344m
当前速度: (0.0014, 0.0062, -0.0008), 速度大小: 0.0065m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.067, Y:-0.506, Z:0.062
速度因子: 0.167, 自适应速度因子: 0.167
最终轴输出: X=-0.002, Y=-0.008, Z=0.000, RotY=0.017
==================================================

=== ROV控制状态 Frame 2330 Time 46.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.611, 9.607, 3.418)
位置误差向量: (-0.076, -0.080, -0.078), 距离: 0.1351m
当前速度: (0.0013, 0.0062, -0.0009), 速度大小: 0.0064m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.064, Y:-0.495, Z:0.061
速度因子: 0.168, 自适应速度因子: 0.168
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.015
==================================================

=== ROV控制状态 Frame 2340 Time 46.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.611, 9.608, 3.418)
位置误差向量: (-0.076, -0.081, -0.078), 距离: 0.1359m
当前速度: (0.0012, 0.0061, -0.0009), 速度大小: 0.0063m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.061, Y:-0.483, Z:0.060
速度因子: 0.169, 自适应速度因子: 0.169
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.041
==================================================

=== ROV控制状态 Frame 2350 Time 47.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.611, 9.609, 3.418)
位置误差向量: (-0.077, -0.082, -0.078), 距离: 0.1367m
当前速度: (0.0012, 0.0060, -0.0009), 速度大小: 0.0062m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.058, Y:-0.472, Z:0.059
速度因子: 0.169, 自适应速度因子: 0.169
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.053
==================================================

=== ROV控制状态 Frame 2360 Time 47.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.611, 9.611, 3.417)
位置误差向量: (-0.077, -0.084, -0.077), 距离: 0.1374m
当前速度: (0.0012, 0.0059, -0.0009), 速度大小: 0.0061m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.056, Y:-0.461, Z:0.058
速度因子: 0.170, 自适应速度因子: 0.170
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.049
==================================================

=== ROV控制状态 Frame 2370 Time 47.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.612, 9.612, 3.417)
位置误差向量: (-0.077, -0.085, -0.077), 距离: 0.1382m
当前速度: (0.0011, 0.0057, -0.0009), 速度大小: 0.0059m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.053, Y:-0.450, Z:0.056
速度因子: 0.171, 自适应速度因子: 0.171
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.030
==================================================

=== ROV控制状态 Frame 2380 Time 47.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.612, 9.613, 3.417)
位置误差向量: (-0.077, -0.086, -0.077), 距离: 0.1389m
当前速度: (0.0010, 0.0056, -0.0009), 速度大小: 0.0058m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.050, Y:-0.439, Z:0.055
速度因子: 0.171, 自适应速度因子: 0.171
最终轴输出: X=-0.001, Y=-0.008, Z=0.000, RotY=-0.004
==================================================

=== ROV控制状态 Frame 2390 Time 47.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.612, 9.614, 3.417)
位置误差向量: (-0.078, -0.087, -0.077), 距离: 0.1396m
当前速度: (0.0010, 0.0055, -0.0009), 速度大小: 0.0057m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.048, Y:-0.428, Z:0.054
速度因子: 0.172, 自适应速度因子: 0.172
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=0.025
==================================================

=== ROV控制状态 Frame 2400 Time 48.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.612, 9.615, 3.417)
位置误差向量: (-0.078, -0.088, -0.077), 距离: 0.1403m
当前速度: (0.0010, 0.0055, -0.0009), 速度大小: 0.0056m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.046, Y:-0.418, Z:0.052
速度因子: 0.172, 自适应速度因子: 0.172
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=0.045
==================================================

=== ROV控制状态 Frame 2410 Time 48.20s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.612, 9.616, 3.416)
位置误差向量: (-0.078, -0.089, -0.076), 距离: 0.1409m
当前速度: (0.0009, 0.0054, -0.0009), 速度大小: 0.0055m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.043, Y:-0.407, Z:0.051
速度因子: 0.173, 自适应速度因子: 0.173
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=0.050
==================================================

=== ROV控制状态 Frame 2420 Time 48.40s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.613, 9.617, 3.416)
位置误差向量: (-0.078, -0.090, -0.076), 距离: 0.1416m
当前速度: (0.0009, 0.0052, -0.0009), 速度大小: 0.0054m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.041, Y:-0.397, Z:0.050
速度因子: 0.173, 自适应速度因子: 0.173
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=0.040
==================================================

=== ROV控制状态 Frame 2430 Time 48.60s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.613, 9.618, 3.416)
位置误差向量: (-0.078, -0.091, -0.076), 距离: 0.1423m
当前速度: (0.0008, 0.0051, -0.0009), 速度大小: 0.0052m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.039, Y:-0.387, Z:0.049
速度因子: 0.174, 自适应速度因子: 0.174
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=0.045
==================================================

=== ROV控制状态 Frame 2440 Time 48.80s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.613, 9.619, 3.416)
位置误差向量: (-0.078, -0.092, -0.076), 距离: 0.1429m
当前速度: (0.0008, 0.0050, -0.0009), 速度大小: 0.0051m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.037, Y:-0.377, Z:0.047
速度因子: 0.174, 自适应速度因子: 0.174
最终轴输出: X=-0.001, Y=-0.007, Z=0.000, RotY=-0.008
==================================================

=== ROV控制状态 Frame 2450 Time 49.00s ===
目标位置: (29.534, 9.527, 3.340), 当前位置: (29.613, 9.620, 3.416)
位置误差向量: (-0.079, -0.093, -0.076), 距离: 0.1435m
当前速度: (0.0007, 0.0049, -0.0009), 速度大小: 0.0050m/s
控制阶段: 精细控制, 控制模式: Balanced
位置控制: True, 旋转控制: True
PID输出 - X:-0.036, Y:-0.367, Z:0.046
速度因子: 0.175, 自适应速度因子: 0.175
最终轴输出: X=-0.001, Y=-0.006, Z=0.000, RotY=-0.034
==================================================


================================================================================
日志结束时间: 2025-07-31 17:22:02
================================================================================
