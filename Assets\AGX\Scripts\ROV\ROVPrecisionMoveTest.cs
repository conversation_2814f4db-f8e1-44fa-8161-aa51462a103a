using UnityEngine;
using System.Collections;

/// <summary>
/// ROV精确移动测试脚本
/// 用于测试优化后的PID控制系统，确保ROV能够精确到达目标位置而不超调
/// </summary>
public class ROVPrecisionMoveTest : MonoBehaviour
{
    [Header("测试配置")]
    public RovController rovController;
    public Transform targetMarker;  // 目标位置标记
    
    [Header("测试目标位置")]
    public Vector3 testTargetPosition = new Vector3(29.534f, 9.527f, 3.340f);
    
    [Header("测试控制")]
    public bool autoStartTest = false;
    public float testDelay = 2f;  // 测试开始延迟
    
    [Header("监控信息")]
    [SerializeField] private float currentDistance;
    [SerializeField] private bool isAtTarget;
    [SerializeField] private Vector3 currentPosition;
    [SerializeField] private Vector3 currentVelocity;
    [SerializeField] private float maxOvershoot;  // 最大超调量
    [SerializeField] private bool testCompleted;
    
    private Vector3 initialPosition;
    private float testStartTime;
    private bool testStarted = false;
    private Vector3 closestApproach;  // 最接近目标的位置
    private float minDistance = float.MaxValue;

    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }
        
        if (rovController == null)
        {
            Debug.LogError("未找到RovController组件！");
            return;
        }

        // 创建目标位置标记
        CreateTargetMarker();
        
        if (autoStartTest)
        {
            StartCoroutine(StartTestWithDelay());
        }
    }

    void Update()
    {
        if (rovController == null || !testStarted) return;
        
        UpdateMonitoringInfo();
        CheckTestProgress();
        
        // 手动测试控制
        if (Input.GetKeyDown(KeyCode.T))
        {
            StartPrecisionMoveTest();
        }
        
        if (Input.GetKeyDown(KeyCode.R))
        {
            ResetTest();
        }
    }

    /// <summary>
    /// 创建目标位置标记
    /// </summary>
    void CreateTargetMarker()
    {
        if (targetMarker == null)
        {
            GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            marker.name = "Target Marker";
            marker.transform.position = testTargetPosition;
            marker.transform.localScale = Vector3.one * 0.2f;
            
            // 设置材质颜色为红色
            Renderer renderer = marker.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material mat = new Material(Shader.Find("Standard"));
                mat.color = Color.red;
                renderer.material = mat;
            }
            
            // 移除碰撞器
            Collider collider = marker.GetComponent<Collider>();
            if (collider != null)
            {
                DestroyImmediate(collider);
            }
            
            targetMarker = marker.transform;
        }
        else
        {
            targetMarker.position = testTargetPosition;
        }
    }

    /// <summary>
    /// 延迟开始测试
    /// </summary>
    IEnumerator StartTestWithDelay()
    {
        yield return new WaitForSeconds(testDelay);
        StartPrecisionMoveTest();
    }

    /// <summary>
    /// 开始精确移动测试
    /// </summary>
    [ContextMenu("开始精确移动测试")]
    public void StartPrecisionMoveTest()
    {
        if (rovController == null) return;
        
        // 重置测试状态
        testStarted = true;
        testCompleted = false;
        testStartTime = Time.time;
        initialPosition = rovController.Rov.RovTransform.position;
        maxOvershoot = 0f;
        minDistance = float.MaxValue;
        closestApproach = initialPosition;
        
        // 更新目标标记位置
        CreateTargetMarker();
        
        // 开始精确移动
        rovController.MoveToTargetPrecise(testTargetPosition);
        
        Debug.Log($"=== 开始ROV精确移动测试 ===");
        Debug.Log($"起始位置: {initialPosition:F3}");
        Debug.Log($"目标位置: {testTargetPosition:F3}");
        Debug.Log($"初始距离: {Vector3.Distance(initialPosition, testTargetPosition):F3}m");
        Debug.Log($"按T键重新开始测试，按R键重置");
    }

    /// <summary>
    /// 更新监控信息
    /// </summary>
    void UpdateMonitoringInfo()
    {
        currentPosition = rovController.Rov.RovTransform.position;
        currentDistance = rovController.GetDistanceToTarget();
        isAtTarget = rovController.IsAtTarget();
        
        // 计算当前速度（如果可以访问）
        if (rovController.Rov.rovRigidBody != null)
        {
            currentVelocity = rovController.Rov.rovRigidBody.velocity;
        }
        
        // 跟踪最接近的位置和最大超调
        if (currentDistance < minDistance)
        {
            minDistance = currentDistance;
            closestApproach = currentPosition;
        }
        
        // 计算超调量（如果已经接近过目标又远离了）
        if (minDistance < 0.1f && currentDistance > minDistance)
        {
            float overshoot = currentDistance - minDistance;
            if (overshoot > maxOvershoot)
            {
                maxOvershoot = overshoot;
            }
        }
    }

    /// <summary>
    /// 检查测试进度
    /// </summary>
    void CheckTestProgress()
    {
        if (testCompleted) return;
        
        // 检查是否到达目标
        if (isAtTarget)
        {
            CompleteTest(true);
        }
        // 检查是否超时（60秒）
        else if (Time.time - testStartTime > 60f)
        {
            CompleteTest(false);
        }
    }

    /// <summary>
    /// 完成测试
    /// </summary>
    void CompleteTest(bool success)
    {
        testCompleted = true;
        float testDuration = Time.time - testStartTime;
        
        Debug.Log($"=== ROV精确移动测试完成 ===");
        Debug.Log($"测试结果: {(success ? "成功" : "超时失败")}");
        Debug.Log($"测试时长: {testDuration:F1}秒");
        Debug.Log($"最终位置: {currentPosition:F3}");
        Debug.Log($"最终距离: {currentDistance:F4}m");
        Debug.Log($"最接近位置: {closestApproach:F3}");
        Debug.Log($"最小距离: {minDistance:F4}m");
        Debug.Log($"最大超调: {maxOvershoot:F4}m");
        Debug.Log($"最终速度: {currentVelocity.magnitude:F4}m/s");
        
        // 评估测试质量
        string quality = EvaluateTestQuality();
        Debug.Log($"移动质量评估: {quality}");
    }

    /// <summary>
    /// 评估测试质量
    /// </summary>
    string EvaluateTestQuality()
    {
        if (maxOvershoot < 0.01f && minDistance < 0.02f)
            return "优秀 - 无超调，高精度";
        else if (maxOvershoot < 0.05f && minDistance < 0.05f)
            return "良好 - 轻微超调，精度良好";
        else if (maxOvershoot < 0.1f && minDistance < 0.1f)
            return "一般 - 有超调，精度一般";
        else
            return "需要改进 - 超调明显或精度不足";
    }

    /// <summary>
    /// 重置测试
    /// </summary>
    [ContextMenu("重置测试")]
    public void ResetTest()
    {
        testStarted = false;
        testCompleted = false;
        rovController?.StopMovement();
        Debug.Log("测试已重置");
    }

    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    void OnDrawGizmos()
    {
        if (!testStarted) return;
        
        // 绘制目标位置
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(testTargetPosition, 0.1f);
        
        // 绘制当前位置
        if (rovController != null)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(currentPosition, 0.05f);
            
            // 绘制连接线
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(currentPosition, testTargetPosition);
        }
        
        // 绘制最接近的位置
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(closestApproach, 0.03f);
    }
}
